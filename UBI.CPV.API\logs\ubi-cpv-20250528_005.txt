2025-05-28 19:18:01.034 +05:30 [ERR] An error occurred while creating the database or seeding data
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 156
2025-05-28 19:18:01.213 +05:30 [INF] UBI-CPV API starting up...
2025-05-28 19:18:01.568 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-28 19:18:02.418 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-28 19:18:02.427 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-28 19:18:02.509 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-28 19:18:02.519 +05:30 [INF] Hosting environment: Development
2025-05-28 19:18:02.528 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-05-28 19:18:03.601 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null
2025-05-28 19:18:04.711 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 1123.555ms
2025-05-28 19:18:04.739 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404
2025-05-28 19:19:12.939 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - null null
2025-05-28 19:19:12.983 +05:30 [INF] CORS policy execution successful.
2025-05-28 19:19:13.001 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - 204 null null 62.2846ms
2025-05-28 19:19:13.018 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/logout - null 0
2025-05-28 19:19:13.037 +05:30 [INF] CORS policy execution successful.
2025-05-28 19:19:13.207 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-28 19:19:13.245 +05:30 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-28 19:19:13.734 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-05-28 19:19:14.367 +05:30 [ERR] Error during logout
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.AuthController.Logout() in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 124
2025-05-28 19:19:14.437 +05:30 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-28 19:19:14.478 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API) in 1201.4655ms
2025-05-28 19:19:14.500 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-28 19:19:14.520 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/logout - 500 null application/json; charset=utf-8 1501.8344ms
2025-05-28 19:19:23.286 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null
2025-05-28 19:19:23.315 +05:30 [INF] CORS policy execution successful.
2025-05-28 19:19:23.330 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 43.7048ms
2025-05-28 19:19:23.352 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 57
2025-05-28 19:19:23.370 +05:30 [INF] CORS policy execution successful.
2025-05-28 19:19:23.388 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-28 19:19:23.426 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-28 19:19:23.663 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-05-28 19:19:24.319 +05:30 [ERR] Error during login for user: admin
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 36
2025-05-28 19:19:24.375 +05:30 [INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'.
2025-05-28 19:19:24.419 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 972.9257ms
2025-05-28 19:19:24.442 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-28 19:19:24.462 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 500 null application/json; charset=utf-8 1110.0009ms
2025-05-28 19:20:25.566 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null
2025-05-28 19:20:25.588 +05:30 [INF] CORS policy execution successful.
2025-05-28 19:20:25.600 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 33.9107ms
2025-05-28 19:20:25.619 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 57
2025-05-28 19:20:25.636 +05:30 [INF] CORS policy execution successful.
2025-05-28 19:20:25.644 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-28 19:20:25.656 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-28 19:20:29.211 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
