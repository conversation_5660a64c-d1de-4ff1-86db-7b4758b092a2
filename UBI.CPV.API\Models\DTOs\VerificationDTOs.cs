using System.ComponentModel.DataAnnotations;

namespace UBI.CPV.API.Models.DTOs
{
    public class VerificationDataDto
    {
        public int VerificationId { get; set; }
        public int LeadId { get; set; }
        public string AgentName { get; set; } = string.Empty;
        public string AgentContact { get; set; } = string.Empty;
        public string? AddressConfirmed { get; set; }
        public string? PersonMet { get; set; }
        public string? Relationship { get; set; }
        public string? OfficeAddress { get; set; }
        public string? OfficeState { get; set; }
        public string? OfficeDistrict { get; set; }
        public string? OfficePincode { get; set; }
        public string? Landmark { get; set; }
        public string? CompanyType { get; set; }
        public string? BusinessNature { get; set; }
        public int? EstablishmentYear { get; set; }
        public string? EmployeesCount { get; set; }
        public decimal? GrossSalary { get; set; }
        public decimal? NetSalary { get; set; }
        public string? ProofType { get; set; }
        public DateTime VerificationDate { get; set; }
        public string? AdditionalNotes { get; set; }
    }

    public class CreateVerificationDataDto
    {
        [Required]
        [StringLength(100)]
        public string AgentName { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        [RegularExpression(@"^\d{10}$", ErrorMessage = "Agent contact must be 10 digits")]
        public string AgentContact { get; set; } = string.Empty;

        public string? AddressConfirmed { get; set; }

        [StringLength(100)]
        public string? PersonMet { get; set; }

        [StringLength(50)]
        public string? Relationship { get; set; }

        [StringLength(500)]
        public string? OfficeAddress { get; set; }

        [StringLength(50)]
        public string? OfficeState { get; set; }

        [StringLength(50)]
        public string? OfficeDistrict { get; set; }

        [StringLength(10)]
        public string? OfficePincode { get; set; }

        [StringLength(200)]
        public string? Landmark { get; set; }

        [StringLength(100)]
        public string? CompanyType { get; set; }

        [StringLength(100)]
        public string? BusinessNature { get; set; }

        [Range(1900, 2100)]
        public int? EstablishmentYear { get; set; }

        [StringLength(20)]
        public string? EmployeesCount { get; set; }

        [Range(0, *********.99)]
        public decimal? GrossSalary { get; set; }

        [Range(0, *********.99)]
        public decimal? NetSalary { get; set; }

        [StringLength(50)]
        public string? ProofType { get; set; }

        [StringLength(1000)]
        public string? AdditionalNotes { get; set; }
    }

    public class UpdateVerificationDataDto
    {
        [Required]
        [StringLength(100)]
        public string AgentName { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        [RegularExpression(@"^\d{10}$", ErrorMessage = "Agent contact must be 10 digits")]
        public string AgentContact { get; set; } = string.Empty;

        public string? AddressConfirmed { get; set; }

        [StringLength(100)]
        public string? PersonMet { get; set; }

        [StringLength(50)]
        public string? Relationship { get; set; }

        [StringLength(500)]
        public string? OfficeAddress { get; set; }

        [StringLength(50)]
        public string? OfficeState { get; set; }

        [StringLength(50)]
        public string? OfficeDistrict { get; set; }

        [StringLength(10)]
        public string? OfficePincode { get; set; }

        [StringLength(200)]
        public string? Landmark { get; set; }

        [StringLength(100)]
        public string? CompanyType { get; set; }

        [StringLength(100)]
        public string? BusinessNature { get; set; }

        [Range(1900, 2100)]
        public int? EstablishmentYear { get; set; }

        [StringLength(20)]
        public string? EmployeesCount { get; set; }

        [Range(0, *********.99)]
        public decimal? GrossSalary { get; set; }

        [Range(0, *********.99)]
        public decimal? NetSalary { get; set; }

        [StringLength(50)]
        public string? ProofType { get; set; }

        [StringLength(1000)]
        public string? AdditionalNotes { get; set; }
    }

    public class AgentDashboardStatsDto
    {
        public int PendingLeads { get; set; }
        public int InProgressLeads { get; set; }
        public int CompletedLeads { get; set; }
        public int RejectedLeads { get; set; }
        public int TotalAssigned { get; set; }
    }

    public class SupervisorDashboardStatsDto
    {
        public int PendingReviews { get; set; }
        public int ApprovedToday { get; set; }
        public int RejectedToday { get; set; }
        public int TotalReviewed { get; set; }
        public decimal ApprovalRate { get; set; }
        public List<AgentPerformanceDto> AgentPerformance { get; set; } = new List<AgentPerformanceDto>();
    }

    public class AgentPerformanceDto
    {
        public int AgentId { get; set; }
        public string AgentName { get; set; } = string.Empty;
        public int AssignedCount { get; set; }
        public int CompletedCount { get; set; }
        public int ApprovedCount { get; set; }
        public int RejectedCount { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal ApprovalRate { get; set; }
    }
}
