[{"ContainingType": "UBI.CPV.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "UBI.CPV.API.Models.DTOs.LoginRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.LoginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "UBI.CPV.API.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "UBI.CPV.API.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "UBI.CPV.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "UBI.CPV.API.Models.DTOs.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.LoginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "UBI.CPV.API.Controllers.DocumentsController", "Method": "UploadCroppedImage", "RelativePath": "api/Documents/leads/{leadId}/cropped-images", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.Int32", "IsRequired": true}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "CropData", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.FileUploadResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.DocumentsController", "Method": "UploadLeadDocument", "RelativePath": "api/Documents/leads/{leadId}/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.Int32", "IsRequired": true}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "DocumentTypeId", "Type": "System.Int32", "IsRequired": false}, {"Name": "DocumentCategory", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.FileUploadResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.DocumentsController", "Method": "UploadVerificationDocument", "RelativePath": "api/Documents/leads/{leadId}/verification-documents", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.Int32", "IsRequired": true}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "DocumentTypeId", "Type": "System.Int32", "IsRequired": false}, {"Name": "DocumentCategory", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.FileUploadResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.DocumentsController", "Method": "GetVerificationDocuments", "RelativePath": "api/Documents/leads/{leadId}/verification-documents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[UBI.CPV.API.Models.DTOs.VerificationDocumentDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.DocumentsController", "Method": "GetDocumentTypes", "RelativePath": "api/Documents/types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[UBI.CPV.API.Models.DTOs.DocumentTypeDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.LeadsController", "Method": "GetLeads", "RelativePath": "api/Leads", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "assignedTo", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "UBI.CPV.API.Controllers.LeadsController", "Method": "CreateLead", "RelativePath": "api/Leads", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createLeadDto", "Type": "UBI.CPV.API.Models.DTOs.CreateLeadDto", "IsRequired": true}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.LeadDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.LeadsController", "Method": "GetLead", "RelativePath": "api/Leads/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.LeadDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.LeadsController", "Method": "AssignLead", "RelativePath": "api/Leads/{id}/assign", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "assignLeadDto", "Type": "UBI.CPV.API.Models.DTOs.AssignLeadDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "UBI.CPV.API.Controllers.LeadsController", "Method": "UpdateLeadStatus", "RelativePath": "api/Leads/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStatusDto", "Type": "UBI.CPV.API.Models.DTOs.UpdateLeadStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "UBI.CPV.API.Controllers.LeadsController", "Method": "GetDashboardStats", "RelativePath": "api/Leads/dashboard-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.DashboardStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.VerificationController", "Method": "GetAgentDashboardStats", "RelativePath": "api/Verification/agent/dashboard-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.VerificationController", "Method": "CreateVerificationData", "RelativePath": "api/Verification/leads/{leadId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createDto", "Type": "UBI.CPV.API.Models.DTOs.CreateVerificationDataDto", "IsRequired": true}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.VerificationDataDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.VerificationController", "Method": "UpdateVerificationData", "RelativePath": "api/Verification/leads/{leadId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDto", "Type": "UBI.CPV.API.Models.DTOs.UpdateVerificationDataDto", "IsRequired": true}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.VerificationDataDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.VerificationController", "Method": "GetVerificationData", "RelativePath": "api/Verification/leads/{leadId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.VerificationDataDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UBI.CPV.API.Controllers.VerificationController", "Method": "GetSupervisorDashboardStats", "RelativePath": "api/Verification/supervisor/dashboard-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_8", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}]