import { User } from './auth.types';
import { Document, CroppedImage } from './document.types';
import { VerificationData } from './verification.types';

export interface CreateLead {
  customerName: string;
  mobileNumber: string;
  loanType: string;
  addresses: CreateAddress[];
}

export interface CreateAddress {
  type: string;
  address: string;
  pincode: string;
  state: string;
  district: string;
  landmark?: string;
}

export interface UpdateLead {
  customerName: string;
  mobileNumber: string;
  loanType: string;
  addresses: UpdateAddress[];
}

export interface UpdateAddress {
  addressId?: number;
  addressType: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  pincode: string;
  landmark?: string;
}

export interface Lead {
  leadId: number;
  customerName: string;
  mobileNumber: string;
  loanType: string;
  status: string;
  createdDate: string;
  assignedDate?: string;
  startedDate?: string;
  submittedDate?: string;
  reviewedDate?: string;
  approvedDate?: string;
  rejectedDate?: string;
  rejectionReason?: string;
  reviewComments?: string;
  creator?: User;
  assignedAgent?: User;
  reviewer?: User;
  addresses: Address[];
  statusHistory: StatusHistory[];
  documents: Document[];
  croppedImages: CroppedImage[];
  verificationData?: VerificationData;
}

export interface Address {
  addressId: number;
  leadId: number;
  addressType: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  pincode: string;
  landmark?: string;
  isActive: boolean;
}

export interface StatusHistory {
  historyId: number;
  leadId: number;
  status: string;
  comments?: string;
  changedDate: string;
  changedByName?: string;
}

export interface UpdateLeadStatus {
  status: string;
  comments?: string;
  rejectionReason?: string;
}

export interface AssignLead {
  agentId: number;
  comments?: string;
}

export interface LeadListItem {
  leadId: number;
  customerName: string;
  mobileNumber: string;
  loanType: string;
  status: string;
  createdDate: string;
  assignedDate?: string;
  createdByName?: string;
  assignedToName?: string;
  reviewedByName?: string;
  documentCount: number;
  croppedImageCount: number;
}

export interface DashboardStats {
  totalLeads: number;
  newLeads: number;
  assignedLeads: number;
  inProgressLeads: number;
  pendingReviewLeads: number;
  approvedLeads: number;
  rejectedLeads: number;
}

export interface PagedResult<T> {
  data: T[];
  totalRecords: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export type LeadStatus = 'New' | 'Assigned' | 'InProgress' | 'PendingReview' | 'Approved' | 'Rejected';
export type LoanType = 'Home Loan' | 'Personal Loan' | 'Business Loan' | 'Car Loan' | 'Education Loan';
export type AddressType = 'Residential' | 'Office' | 'Business';
