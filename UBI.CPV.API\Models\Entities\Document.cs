using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace UBI.CPV.API.Models.Entities
{
    public class DocumentType
    {
        [Key]
        public int DocumentTypeId { get; set; }

        [Required]
        [StringLength(50)]
        public string TypeName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<LeadDocument> LeadDocuments { get; set; } = new List<LeadDocument>();
        public virtual ICollection<VerificationDocument> VerificationDocuments { get; set; } = new List<VerificationDocument>();
    }

    public class LeadDocument
    {
        [Key]
        public int DocumentId { get; set; }

        [Required]
        public int LeadId { get; set; }

        [Required]
        public int DocumentTypeId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        public long FileSize { get; set; }

        [Required]
        [StringLength(100)]
        public string MimeType { get; set; } = string.Empty;

        public DateTime UploadedDate { get; set; } = DateTime.UtcNow;

        [Required]
        public int UploadedBy { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        [ForeignKey("LeadId")]
        public virtual Lead Lead { get; set; } = null!;

        [ForeignKey("DocumentTypeId")]
        public virtual DocumentType DocumentType { get; set; } = null!;

        [ForeignKey("UploadedBy")]
        public virtual User UploadedByUser { get; set; } = null!;
    }

    public class CroppedImage
    {
        [Key]
        public int ImageId { get; set; }

        [Required]
        public int LeadId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        public long FileSize { get; set; }

        [Required]
        [StringLength(100)]
        public string MimeType { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? CropData { get; set; }

        public int? PageNumber { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Required]
        public int CreatedBy { get; set; }

        // Navigation properties
        [ForeignKey("LeadId")]
        public virtual Lead Lead { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; } = null!;
    }

    public class VerificationDocument
    {
        [Key]
        public int DocumentId { get; set; }

        [Required]
        public int LeadId { get; set; }

        [Required]
        public int DocumentTypeId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        public long FileSize { get; set; }

        [Required]
        [StringLength(100)]
        public string MimeType { get; set; } = string.Empty;

        public DateTime UploadedDate { get; set; } = DateTime.UtcNow;

        [Required]
        public int UploadedBy { get; set; }

        [StringLength(50)]
        public string? DocumentCategory { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        [ForeignKey("LeadId")]
        public virtual Lead Lead { get; set; } = null!;

        [ForeignKey("DocumentTypeId")]
        public virtual DocumentType DocumentType { get; set; } = null!;

        [ForeignKey("UploadedBy")]
        public virtual User UploadedByUser { get; set; } = null!;
    }
}
