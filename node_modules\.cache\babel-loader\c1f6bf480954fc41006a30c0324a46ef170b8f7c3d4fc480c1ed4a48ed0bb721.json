{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\UBI-CPV-T\\\\src\\\\components\\\\common\\\\LoadingSpinner.tsx\";\nimport React from 'react';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  message = 'Loading...',\n  size = 40,\n  fullScreen = false\n}) => {\n  const containerProps = fullScreen ? {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n    zIndex: 9999\n  } : {};\n  return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    flexDirection: \"column\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    minHeight: fullScreen ? '100vh' : '200px',\n    gap: 2,\n    ...containerProps,\n    children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n      size: size\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"text.secondary\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "Box", "CircularProgress", "Typography", "jsxDEV", "_jsxDEV", "LoadingSpinner", "message", "size", "fullScreen", "containerProps", "position", "top", "left", "right", "bottom", "backgroundColor", "zIndex", "display", "flexDirection", "justifyContent", "alignItems", "minHeight", "gap", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/UBI-CPV-T/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, CircularProgress, Typography } from '@mui/material';\n\ninterface LoadingSpinnerProps {\n  message?: string;\n  size?: number;\n  fullScreen?: boolean;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  message = 'Loading...', \n  size = 40,\n  fullScreen = false \n}) => {\n  const containerProps = fullScreen\n    ? {\n        position: 'fixed' as const,\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(255, 255, 255, 0.8)',\n        zIndex: 9999\n      }\n    : {};\n\n  return (\n    <Box\n      display=\"flex\"\n      flexDirection=\"column\"\n      justifyContent=\"center\"\n      alignItems=\"center\"\n      minHeight={fullScreen ? '100vh' : '200px'}\n      gap={2}\n      {...containerProps}\n    >\n      <CircularProgress size={size} />\n      {message && (\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {message}\n        </Typography>\n      )}\n    </Box>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQlE,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,OAAO,GAAG,YAAY;EACtBC,IAAI,GAAG,EAAE;EACTC,UAAU,GAAG;AACf,CAAC,KAAK;EACJ,MAAMC,cAAc,GAAGD,UAAU,GAC7B;IACEE,QAAQ,EAAE,OAAgB;IAC1BC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE;EACV,CAAC,GACD,CAAC,CAAC;EAEN,oBACEZ,OAAA,CAACJ,GAAG;IACFiB,OAAO,EAAC,MAAM;IACdC,aAAa,EAAC,QAAQ;IACtBC,cAAc,EAAC,QAAQ;IACvBC,UAAU,EAAC,QAAQ;IACnBC,SAAS,EAAEb,UAAU,GAAG,OAAO,GAAG,OAAQ;IAC1Cc,GAAG,EAAE,CAAE;IAAA,GACHb,cAAc;IAAAc,QAAA,gBAElBnB,OAAA,CAACH,gBAAgB;MAACM,IAAI,EAAEA;IAAK;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC/BrB,OAAO,iBACNF,OAAA,CAACF,UAAU;MAAC0B,OAAO,EAAC,OAAO;MAACC,KAAK,EAAC,gBAAgB;MAAAN,QAAA,EAC/CjB;IAAO;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACG,EAAA,GAnCIzB,cAA6C;AAqCnD,eAAeA,cAAc;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}