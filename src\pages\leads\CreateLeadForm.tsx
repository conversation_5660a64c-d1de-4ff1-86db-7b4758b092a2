import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  MenuItem,
  Alert,
} from '@mui/material';
import {
  ArrowBack,
  Save,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { leadService } from '../../services/lead.service';
import { CreateLead } from '../../types/lead.types';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const CreateLeadForm: React.FC = () => {
  const navigate = useNavigate();
  const { hasRole } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Form state
  const [customerName, setCustomerName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [loanType, setLoanType] = useState('Home Loan');
  const [addressType, setAddressType] = useState('Residential');
  const [addressLine1, setAddressLine1] = useState('');
  const [addressLine2, setAddressLine2] = useState('');
  const [district, setDistrict] = useState('');
  const [state, setState] = useState('');
  const [pincode, setPincode] = useState('');
  const [landmark, setLandmark] = useState('');

  // Check if user has Admin role
  React.useEffect(() => {
    if (!hasRole('Admin')) {
      navigate('/leads');
    }
  }, [hasRole, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError('');

      // Validate required fields
      if (!customerName.trim()) {
        setError('Customer name is required');
        return;
      }
      if (!mobileNumber.trim() || !/^\d{10}$/.test(mobileNumber)) {
        setError('Valid 10-digit mobile number is required');
        return;
      }
      if (!addressLine1.trim()) {
        setError('Address line 1 is required');
        return;
      }
      if (!district.trim()) {
        setError('District is required');
        return;
      }
      if (!state.trim()) {
        setError('State is required');
        return;
      }
      if (!pincode.trim() || !/^\d{6}$/.test(pincode)) {
        setError('Valid 6-digit pincode is required');
        return;
      }

      // Transform form data to API format
      const createLeadData: CreateLead = {
        customerName: customerName.trim(),
        mobileNumber: mobileNumber.trim(),
        loanType: loanType,
        addresses: [{
          type: addressType,
          address: addressLine2.trim() 
            ? `${addressLine1.trim()}, ${addressLine2.trim()}` 
            : addressLine1.trim(),
          pincode: pincode.trim(),
          state: state.trim(),
          district: district.trim(),
          landmark: landmark.trim() || undefined,
        }],
      };

      console.log('Sending lead data:', createLeadData);
      const newLead = await leadService.createLead(createLeadData);
      navigate(`/leads/${newLead.leadId}`);
    } catch (err: any) {
      console.error('Create lead error:', err);
      console.error('Error response:', err.response);
      
      let errorMessage = 'Failed to create lead';
      if (err.response?.status === 403) {
        errorMessage = 'Access denied. Only Admin users can create leads.';
      } else if (err.response?.status === 400) {
        errorMessage = err.response?.data?.message || 'Invalid data provided. Please check all fields.';
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/leads');
  };

  const loanTypes = [
    'Home Loan',
    'Personal Loan',
    'Business Loan',
    'Car Loan',
    'Education Loan',
  ];

  const addressTypes = [
    'Residential',
    'Office',
    'Business',
  ];

  if (loading) {
    return <LoadingSpinner message="Creating lead..." />;
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBack />}
          onClick={handleBack}
          sx={{ mr: 2 }}
        >
          Back
        </Button>
        <Box>
          <Typography variant="h4" gutterBottom>
            Create New Lead
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            Enter customer details and verification address
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        {/* Customer Information */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Customer Information
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Customer Name"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Mobile Number"
                value={mobileNumber}
                onChange={(e) => setMobileNumber(e.target.value)}
                required
                inputProps={{ maxLength: 10 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                select
                label="Loan Type"
                value={loanType}
                onChange={(e) => setLoanType(e.target.value)}
                required
              >
                {loanTypes.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
          </Grid>
        </Paper>

        {/* Address */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Address Information
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                select
                label="Address Type"
                value={addressType}
                onChange={(e) => setAddressType(e.target.value)}
                required
              >
                {addressTypes.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Address Line 1"
                value={addressLine1}
                onChange={(e) => setAddressLine1(e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Address Line 2 (Optional)"
                value={addressLine2}
                onChange={(e) => setAddressLine2(e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="District"
                value={district}
                onChange={(e) => setDistrict(e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="State"
                value={state}
                onChange={(e) => setState(e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Pincode"
                value={pincode}
                onChange={(e) => setPincode(e.target.value)}
                required
                inputProps={{ maxLength: 6 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Landmark (Optional)"
                value={landmark}
                onChange={(e) => setLandmark(e.target.value)}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Actions */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            onClick={handleBack}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
          >
            Create Lead
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default CreateLeadForm;
