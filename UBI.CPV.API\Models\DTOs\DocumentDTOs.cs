using System.ComponentModel.DataAnnotations;

namespace UBI.CPV.API.Models.DTOs
{
    public class DocumentDto
    {
        public int DocumentId { get; set; }
        public int LeadId { get; set; }
        public string DocumentTypeName { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public DateTime UploadedDate { get; set; }
        public string? UploadedByName { get; set; }
        public bool IsActive { get; set; }
    }

    public class CroppedImageDto
    {
        public int ImageId { get; set; }
        public int LeadId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public string? CropData { get; set; }
        public int? PageNumber { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? CreatedByName { get; set; }
    }

    public class UploadDocumentDto
    {
        [Required]
        public IFormFile File { get; set; } = null!;

        [Required]
        public int DocumentTypeId { get; set; }

        public string? DocumentCategory { get; set; }
    }

    public class UploadCroppedImageDto
    {
        [Required]
        public IFormFile File { get; set; } = null!;

        public string? CropData { get; set; }

        public int? PageNumber { get; set; }
    }

    public class DocumentTypeDto
    {
        public int DocumentTypeId { get; set; }
        public string TypeName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
    }

    public class FileUploadResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? FileName { get; set; }
        public string? FilePath { get; set; }
        public long FileSize { get; set; }
        public int? DocumentId { get; set; }
        public int? ImageId { get; set; }
    }

    public class VerificationDocumentDto
    {
        public int DocumentId { get; set; }
        public int LeadId { get; set; }
        public string DocumentTypeName { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public DateTime UploadedDate { get; set; }
        public string? UploadedByName { get; set; }
        public string? DocumentCategory { get; set; }
        public bool IsActive { get; set; }
    }

    public class UploadVerificationDocumentDto
    {
        [Required]
        public IFormFile File { get; set; } = null!;

        [Required]
        public int DocumentTypeId { get; set; }

        [Required]
        public string DocumentCategory { get; set; } = string.Empty;
    }
}
