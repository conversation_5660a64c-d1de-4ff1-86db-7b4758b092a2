[2025-06-05 17:04:00.432 +05:30 INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-05 17:04:00.646 +05:30 INF] Executed DbCommand (76ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-05 17:04:00.659 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-05 17:04:00.673 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-05 17:04:00.811 +05:30 INF] Executed DbCommand (111ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId]; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-05 17:04:00.867 +05:30 INF] Applying migration '20250529130711_InitialCreate'. {"EventId":{"Id":20402,"Name":"Microsoft.EntityFrameworkCore.Migrations.MigrationApplying"},"SourceContext":"Microsoft.EntityFrameworkCore.Migrations"}
[2025-06-05 17:04:01.055 +05:30 ERR] Failed executing DbCommand (68ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
); {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-05 17:04:01.139 +05:30 ERR] An error occurred while creating the database or seeding data {}
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\UBI-CPV-T\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:ebe1896a-b1c5-4560-9e9a-b87b311f472c
Error Number:2714,State:6,Class:16
[2025-06-05 17:04:01.168 +05:30 INF] UBI-CPV API starting up... {}
[2025-06-05 17:04:01.355 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-06-05 17:04:02.072 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-05 17:04:02.082 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-05 17:04:02.188 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-05 17:04:02.196 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-05 17:04:02.201 +05:30 INF] Content root path: D:\Augment-projects\UBI-CPV-T\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-05 17:04:11.357 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053J:********","RequestPath":"/","ConnectionId":"0HND44JFE053J"}
[2025-06-05 17:04:12.526 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 1178.2481ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053J:********","RequestPath":"/","ConnectionId":"0HND44JFE053J"}
[2025-06-05 17:04:12.592 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053J:********","RequestPath":"/","ConnectionId":"0HND44JFE053J"}
[2025-06-05 17:04:50.052 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:50.210 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:50.241 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Auth/login - 204 null null 189.5288ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:50.276 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:59359/api/Auth/login - application/json 55 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:50.300 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:50.493 +05:30 INF] Failed to validate the token. {"EventId":{"Id":1,"Name":"TokenValidationFailed"},"SourceContext":"Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
[2025-06-05 17:04:50.543 +05:30 INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details. {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:50.592 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:50.676 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:54.294 +05:30 INF] Executed DbCommand (254ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.512 +05:30 INF] Executed DbCommand (91ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.649 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.703 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 4996.0858ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.725 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.749 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:59359/api/Auth/login - 200 null application/json; charset=utf-8 5473.1926ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.891 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.891 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:55.930 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.934 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:55.960 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - 204 null null 70.3689ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053K:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053K"}
[2025-06-05 17:04:55.961 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - 204 null null 70.055ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.012 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.030 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.093 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.128 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.214 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/favicon.ico - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053M:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053M"}
[2025-06-05 17:04:56.332 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/favicon.ico - 404 0 null 118.0671ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053M:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053M"}
[2025-06-05 17:04:56.369 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:59359/favicon.ico, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053M:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053M"}
[2025-06-05 17:04:56.456 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/favicon.ico - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053N:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053N"}
[2025-06-05 17:04:56.489 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/favicon.ico - 404 0 null 33.5888ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053N:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053N"}
[2025-06-05 17:04:56.531 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:59359/favicon.ico, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053N:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053N"}
[2025-06-05 17:04:56.551 +05:30 WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results. {"EventId":{"Id":10103,"Name":"Microsoft.EntityFrameworkCore.Query.FirstWithoutOrderByAndFilterWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.683 +05:30 INF] Executed DbCommand (68ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.732 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.775 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 604.2579ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.798 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.812 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - 200 null application/json; charset=utf-8 799.4723ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.847 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.892 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.915 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.926 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.963 +05:30 INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:56.989 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:57.013 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 69.4358ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:57.040 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:04:57.065 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - 200 null application/json; charset=utf-8 217.9578ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053L:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE053L"}
[2025-06-05 17:05:12.510 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:12.510 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053P:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053P"}
[2025-06-05 17:05:12.567 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:12.576 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053P:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053P"}
[2025-06-05 17:05:12.615 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 204 null null 104.9615ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:12.618 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 204 null null 107.8644ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053P:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053P"}
[2025-06-05 17:05:12.690 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:12.728 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:12.754 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:12.787 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:12.914 +05:30 INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.183 +05:30 INF] Executed DbCommand (129ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.221 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.249 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 423.7086ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.276 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.289 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 598.99ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.312 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.330 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.344 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.360 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.390 +05:30 INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.460 +05:30 INF] Executed DbCommand (39ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.502 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.524 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 142.8341ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.538 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:05:13.551 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 239.2891ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053O:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053O"}
[2025-06-05 17:07:12.321 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.336 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:12.385 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.394 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:12.403 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/10 - 204 null null 81.5773ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.440 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/10 - 204 null null 104.4325ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:12.448 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads/10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.485 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.503 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.527 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"e016653b-5f0a-4d68-8a6d-fec891e3184a","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.733 +05:30 WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'. {"EventId":{"Id":20504,"Name":"Microsoft.EntityFrameworkCore.Query.MultipleCollectionIncludeWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"e016653b-5f0a-4d68-8a6d-fec891e3184a","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:12.991 +05:30 INF] Executed DbCommand (193ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"e016653b-5f0a-4d68-8a6d-fec891e3184a","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.233 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"e016653b-5f0a-4d68-8a6d-fec891e3184a","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.289 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 735.8353ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.306 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.317 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads/10 - 200 null application/json; charset=utf-8 869.001ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.338 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads/10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.353 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.366 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.376 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"e016653b-5f0a-4d68-8a6d-fec891e3184a","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.398 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"e016653b-5f0a-4d68-8a6d-fec891e3184a","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.420 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"e016653b-5f0a-4d68-8a6d-fec891e3184a","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.436 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 42.85ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.451 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:13.464 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads/10 - 200 null application/json; charset=utf-8 125.587ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads/10","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:26.694 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:26.694 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:26.739 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:26.752 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:26.790 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 204 null null 95.9292ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:26.794 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 204 null null 99.9099ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053Q:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053Q"}
[2025-06-05 17:07:26.888 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:26.938 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:26.969 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.002 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.044 +05:30 INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.118 +05:30 INF] Executed DbCommand (50ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.150 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.179 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 155.6674ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.207 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.226 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 338.0762ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.255 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.279 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.295 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.310 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.343 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.391 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.420 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.441 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 106.5486ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.467 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:27.485 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 229.6359ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.361 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10&status=InProgress - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.389 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.407 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10&status=InProgress - 204 null null 46.0489ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.438 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10&status=InProgress - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.466 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.488 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.511 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.564 +05:30 INF] Executed DbCommand (11ms) [Parameters=[@__status_0='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[Status] = @__status_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.638 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__status_0='?' (Size = 20), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[Status] = @__status_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.670 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.697 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 153.1721ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.723 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:38.740 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10&status=InProgress - 200 null application/json; charset=utf-8 302.6139ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:40.944 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:40.972 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:40.989 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 204 null null 45.0019ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.020 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.046 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.070 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.102 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.146 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.209 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.246 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"7080adff-7043-484b-a771-be74ccdeec01","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.269 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 134.6134ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.289 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:07:41.306 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 285.8804ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.308 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.337 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.356 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads - 204 null null 48.2917ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:********","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.421 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:59359/api/Leads - application/json 258 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.454 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.476 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.502 +05:30 INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"1364b4c4-3fd3-4c04-b77c-1c79559a8c2c","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.566 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"1364b4c4-3fd3-4c04-b77c-1c79559a8c2c","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.653 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 121.6455ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.689 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:07.714 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:59359/api/Leads - 400 null application/problem+json; charset=utf-8 292.571ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000A","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:36.845 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053U:********","RequestPath":"/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf","ConnectionId":"0HND44JFE053U"}
[2025-06-05 17:09:37.201 +05:30 INF] Sending file. Request path: '/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf'. Physical path: 'D:\Augment-projects\UBI-CPV-T\UBI.CPV.API\wwwroot\uploads\9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HND44JFE053U:********","RequestPath":"/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf","ConnectionId":"0HND44JFE053U"}
[2025-06-05 17:09:37.223 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf - 200 509533 application/pdf 378.3014ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053U:********","RequestPath":"/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf","ConnectionId":"0HND44JFE053U"}
[2025-06-05 17:09:37.341 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/favicon.ico - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053U:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053U"}
[2025-06-05 17:09:37.381 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/favicon.ico - 404 0 null 40.3472ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053U:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053U"}
[2025-06-05 17:09:37.410 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/favicon.ico, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053U:********","RequestPath":"/favicon.ico","ConnectionId":"0HND44JFE053U"}
[2025-06-05 17:09:57.711 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000B","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.743 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:0000000B","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.765 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads - 204 null null 53.5131ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000B","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.793 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:59359/api/Leads - application/json 258 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.826 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.849 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.864 +05:30 INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"1364b4c4-3fd3-4c04-b77c-1c79559a8c2c","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.893 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"1364b4c4-3fd3-4c04-b77c-1c79559a8c2c","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.920 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 29.2832ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.939 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:09:57.959 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:59359/api/Leads - 400 null application/problem+json; charset=utf-8 165.744ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000C","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.150 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000D","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.184 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:0000000D","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.201 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads - 204 null null 51.5588ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000D","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.223 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:59359/api/Leads - application/json 258 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.251 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.268 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.284 +05:30 INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"1364b4c4-3fd3-4c04-b77c-1c79559a8c2c","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.322 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"1364b4c4-3fd3-4c04-b77c-1c79559a8c2c","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.343 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 22.1475ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.365 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:10:21.388 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:59359/api/Leads - 400 null application/problem+json; charset=utf-8 165.0182ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE053R:0000000E","RequestPath":"/api/Leads","ConnectionId":"0HND44JFE053R"}
[2025-06-05 17:13:10.475 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Auth/logout - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.515 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.536 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Auth/logout - 204 null null 61.9411ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.599 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:59359/api/Auth/logout - null 0 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.632 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.652 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.677 +05:30 INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"117ba6f7-0246-417b-822b-8f57004ba044","ActionName":"UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.747 +05:30 INF] Executed DbCommand (22ms) [Parameters=[@__token_0='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[SessionId], [u].[CreatedAt], [u].[ExpiresAt], [u].[IsActive], [u].[RefreshToken], [u].[Token], [u].[UserId]
FROM [UserSessions] AS [u]
WHERE [u].[Token] = @__token_0 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"117ba6f7-0246-417b-822b-8f57004ba044","ActionName":"UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.790 +05:30 INF] Executed DbCommand (8ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [UserSessions] SET [IsActive] = @p0
OUTPUT 1
WHERE [SessionId] = @p1; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"117ba6f7-0246-417b-822b-8f57004ba044","ActionName":"UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.829 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"117ba6f7-0246-417b-822b-8f57004ba044","ActionName":"UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.861 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API) in 155.0051ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.885 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:10.911 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:59359/api/Auth/logout - 200 null application/json; charset=utf-8 312.199ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/logout","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:19.133 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:19.158 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:19.179 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Auth/login - 204 null null 45.9019ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:19.233 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:59359/api/Auth/login - application/json 55 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:19.257 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:19.272 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:19.286 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:24.934 +05:30 INF] Executed DbCommand (19ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.180 +05:30 INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.221 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"0b2696a6-0cf8-484d-a528-8dd6cc470ed7","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.256 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 5950.8296ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.275 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.293 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:59359/api/Auth/login - 200 null application/json; charset=utf-8 6059.766ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Auth/login","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.388 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.388 +05:30 INF] Request starting HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054G:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054G"}
[2025-06-05 17:13:25.436 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.441 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054G:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054G"}
[2025-06-05 17:13:25.465 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - 204 null null 77.184ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.467 +05:30 INF] Request finished HTTP/1.1 OPTIONS http://localhost:59359/api/Leads/dashboard-stats - 204 null null 78.7512ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054G:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054G"}
[2025-06-05 17:13:25.513 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.536 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.549 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.562 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.591 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.615 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.637 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 54.2128ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.656 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.678 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - 200 null application/json; charset=utf-8 164.8628ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.715 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.731 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.742 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.755 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.782 +05:30 INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.800 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"c67bf6da-ac36-4c16-b6d8-50683f569db7","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.814 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 43.1311ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.827 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:13:25.841 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:59359/api/Leads/dashboard-stats - 200 null application/json; charset=utf-8 125.5939ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054F:********","RequestPath":"/api/Leads/dashboard-stats","ConnectionId":"0HND44JFE054F"}
[2025-06-05 17:14:01.805 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054L:********","RequestPath":"/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf","ConnectionId":"0HND44JFE054L"}
[2025-06-05 17:14:02.110 +05:30 INF] Sending file. Request path: '/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf'. Physical path: 'D:\Augment-projects\UBI-CPV-T\UBI.CPV.API\wwwroot\uploads\9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HND44JFE054L:********","RequestPath":"/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf","ConnectionId":"0HND44JFE054L"}
[2025-06-05 17:14:02.146 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf - 200 509533 application/pdf 341.2646ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND44JFE054L:********","RequestPath":"/uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf","ConnectionId":"0HND44JFE054L"}
