using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;
using UBI.CPV.API.Data;
using UBI.CPV.API.Models.DTOs;
using UBI.CPV.API.Models.Entities;

namespace UBI.CPV.API.Controllers
{
    /// <summary>
    /// Lead management controller for creating, updating, and retrieving customer verification leads
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [SwaggerTag("Lead management endpoints for customer verification processes")]
    public class LeadsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<LeadsController> _logger;

        public LeadsController(ApplicationDbContext context, ILogger<LeadsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Retrieves a paginated list of leads with optional filtering
        /// </summary>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Number of items per page (default: 10)</param>
        /// <param name="status">Filter by lead status (optional)</param>
        /// <param name="assignedTo">Filter by assigned agent ID (optional)</param>
        /// <returns>Paginated list of leads</returns>
        /// <response code="200">Leads retrieved successfully</response>
        /// <response code="401">Unauthorized - invalid or missing token</response>
        /// <response code="500">Internal server error</response>
        [HttpGet]
        [SwaggerOperation(
            Summary = "Get Leads",
            Description = "Retrieves a paginated list of leads. Agents can only see leads assigned to them.",
            OperationId = "GetLeads"
        )]
        [SwaggerResponse(200, "Paginated list of leads", typeof(PagedResultDto<LeadListDto>))]
        [SwaggerResponse(401, "Unauthorized")]
        [SwaggerResponse(500, "Internal server error")]
        public async Task<ActionResult<PagedResultDto<LeadListDto>>> GetLeads(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? status = null,
            [FromQuery] int? assignedTo = null)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

                var query = _context.Leads
                    .Include(l => l.Creator)
                    .Include(l => l.AssignedAgent)
                    .Include(l => l.Reviewer)
                    .Include(l => l.Documents)
                    .Include(l => l.CroppedImages)
                    .AsQueryable();

                // Apply role-based filtering
                if (currentUserRole == "Agent")
                {
                    query = query.Where(l => l.AssignedTo == currentUserId);
                }

                // Apply filters
                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(l => l.Status == status);
                }

                if (assignedTo.HasValue)
                {
                    query = query.Where(l => l.AssignedTo == assignedTo.Value);
                }

                var totalRecords = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                var leads = await query
                    .OrderByDescending(l => l.CreatedDate)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(l => new LeadListDto
                    {
                        LeadId = l.LeadId,
                        CustomerName = l.CustomerName,
                        MobileNumber = l.MobileNumber,
                        LoanType = l.LoanType,
                        Status = l.Status,
                        CreatedDate = l.CreatedDate,
                        AssignedDate = l.AssignedDate,
                        CreatedByName = l.Creator != null ? $"{l.Creator.FirstName} {l.Creator.LastName}" : null,
                        AssignedToName = l.AssignedAgent != null ? $"{l.AssignedAgent.FirstName} {l.AssignedAgent.LastName}" : null,
                        ReviewedByName = l.Reviewer != null ? $"{l.Reviewer.FirstName} {l.Reviewer.LastName}" : null,
                        DocumentCount = l.Documents.Count(d => d.IsActive),
                        CroppedImageCount = l.CroppedImages.Count()
                    })
                    .ToListAsync();

                return Ok(new PagedResultDto<LeadListDto>
                {
                    Data = leads,
                    TotalRecords = totalRecords,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = totalPages,
                    HasNextPage = pageNumber < totalPages,
                    HasPreviousPage = pageNumber > 1
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leads");
                return StatusCode(500, new { Message = "An error occurred while retrieving leads." });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<LeadDto>> GetLead(int id)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

                var query = _context.Leads
                    .Include(l => l.Creator)
                    .Include(l => l.AssignedAgent)
                    .Include(l => l.Reviewer)
                    .Include(l => l.Addresses)
                    .Include(l => l.StatusHistory)
                        .ThenInclude(sh => sh.UpdatedByUser)
                    .Include(l => l.Documents)
                        .ThenInclude(d => d.DocumentType)
                    .Include(l => l.Documents)
                        .ThenInclude(d => d.UploadedByUser)
                    .Include(l => l.CroppedImages)
                        .ThenInclude(ci => ci.CreatedByUser)
                    .Include(l => l.VerificationData)
                    .AsQueryable();

                // Apply role-based filtering
                if (currentUserRole == "Agent")
                {
                    query = query.Where(l => l.AssignedTo == currentUserId);
                }

                var lead = await query.FirstOrDefaultAsync(l => l.LeadId == id);

                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                var leadDto = new LeadDto
                {
                    LeadId = lead.LeadId,
                    CustomerName = lead.CustomerName,
                    MobileNumber = lead.MobileNumber,
                    LoanType = lead.LoanType,
                    Status = lead.Status,
                    CreatedDate = lead.CreatedDate,
                    AssignedDate = lead.AssignedDate,
                    StartedDate = lead.StartedDate,
                    SubmittedDate = lead.SubmittedDate,
                    ReviewedDate = lead.ReviewedDate,
                    ApprovedDate = lead.ApprovedDate,
                    RejectedDate = lead.RejectedDate,
                    RejectionReason = lead.RejectionReason,
                    ReviewComments = lead.ReviewComments,
                    Creator = lead.Creator != null ? new UserDto
                    {
                        UserId = lead.Creator.UserId,
                        Username = lead.Creator.Username,
                        FirstName = lead.Creator.FirstName,
                        LastName = lead.Creator.LastName,
                        Role = lead.Creator.Role
                    } : null,
                    AssignedAgent = lead.AssignedAgent != null ? new UserDto
                    {
                        UserId = lead.AssignedAgent.UserId,
                        Username = lead.AssignedAgent.Username,
                        FirstName = lead.AssignedAgent.FirstName,
                        LastName = lead.AssignedAgent.LastName,
                        Role = lead.AssignedAgent.Role
                    } : null,
                    Reviewer = lead.Reviewer != null ? new UserDto
                    {
                        UserId = lead.Reviewer.UserId,
                        Username = lead.Reviewer.Username,
                        FirstName = lead.Reviewer.FirstName,
                        LastName = lead.Reviewer.LastName,
                        Role = lead.Reviewer.Role
                    } : null,
                    Addresses = lead.Addresses.Select(a => new AddressDto
                    {
                        AddressId = a.AddressId,
                        AddressType = a.AddressType,
                        Address = a.Address,
                        Pincode = a.Pincode,
                        State = a.State,
                        District = a.District,
                        Landmark = a.Landmark,
                        CreatedDate = a.CreatedDate
                    }).ToList(),
                    StatusHistory = lead.StatusHistory.OrderByDescending(sh => sh.Timestamp).Select(sh => new StatusHistoryDto
                    {
                        HistoryId = sh.HistoryId,
                        Status = sh.Status,
                        Timestamp = sh.Timestamp,
                        Comments = sh.Comments,
                        UpdatedByUser = sh.UpdatedByUser != null ? new UserDto
                        {
                            UserId = sh.UpdatedByUser.UserId,
                            Username = sh.UpdatedByUser.Username,
                            FirstName = sh.UpdatedByUser.FirstName,
                            LastName = sh.UpdatedByUser.LastName,
                            Role = sh.UpdatedByUser.Role
                        } : null
                    }).ToList(),
                    Documents = lead.Documents.Where(d => d.IsActive).Select(d => new DocumentDto
                    {
                        DocumentId = d.DocumentId,
                        LeadId = d.LeadId,
                        DocumentTypeName = d.DocumentType.TypeName,
                        FileName = d.FileName,
                        OriginalFileName = d.OriginalFileName,
                        FilePath = d.FilePath,
                        FileSize = d.FileSize,
                        MimeType = d.MimeType,
                        UploadedDate = d.UploadedDate,
                        UploadedByName = d.UploadedByUser != null ? $"{d.UploadedByUser.FirstName} {d.UploadedByUser.LastName}" : null,
                        IsActive = d.IsActive
                    }).ToList(),
                    CroppedImages = lead.CroppedImages.Select(ci => new CroppedImageDto
                    {
                        ImageId = ci.ImageId,
                        LeadId = ci.LeadId,
                        FileName = ci.FileName,
                        OriginalFileName = ci.OriginalFileName,
                        FilePath = ci.FilePath,
                        FileSize = ci.FileSize,
                        MimeType = ci.MimeType,
                        CropData = ci.CropData,
                        PageNumber = ci.PageNumber,
                        CreatedDate = ci.CreatedDate,
                        CreatedByName = ci.CreatedByUser != null ? $"{ci.CreatedByUser.FirstName} {ci.CreatedByUser.LastName}" : null
                    }).ToList(),
                    VerificationData = lead.VerificationData != null ? new VerificationDataDto
                    {
                        VerificationId = lead.VerificationData.VerificationId,
                        LeadId = lead.VerificationData.LeadId,
                        AgentName = lead.VerificationData.AgentName,
                        AgentContact = lead.VerificationData.AgentContact,
                        AddressConfirmed = lead.VerificationData.AddressConfirmed,
                        PersonMet = lead.VerificationData.PersonMet,
                        Relationship = lead.VerificationData.Relationship,
                        OfficeAddress = lead.VerificationData.OfficeAddress,
                        OfficeState = lead.VerificationData.OfficeState,
                        OfficeDistrict = lead.VerificationData.OfficeDistrict,
                        OfficePincode = lead.VerificationData.OfficePincode,
                        Landmark = lead.VerificationData.Landmark,
                        CompanyType = lead.VerificationData.CompanyType,
                        BusinessNature = lead.VerificationData.BusinessNature,
                        EstablishmentYear = lead.VerificationData.EstablishmentYear,
                        EmployeesCount = lead.VerificationData.EmployeesCount,
                        GrossSalary = lead.VerificationData.GrossSalary,
                        NetSalary = lead.VerificationData.NetSalary,
                        ProofType = lead.VerificationData.ProofType,
                        VerificationDate = lead.VerificationData.VerificationDate,
                        AdditionalNotes = lead.VerificationData.AdditionalNotes
                    } : null
                };

                return Ok(leadDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lead {LeadId}", id);
                return StatusCode(500, new { Message = "An error occurred while retrieving the lead." });
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<LeadDto>> CreateLead([FromBody] CreateLeadDto createLeadDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                using var transaction = await _context.Database.BeginTransactionAsync();

                // Create lead
                var lead = new Lead
                {
                    CustomerName = createLeadDto.CustomerName,
                    MobileNumber = createLeadDto.MobileNumber,
                    LoanType = createLeadDto.LoanType,
                    Status = "new",
                    CreatedBy = currentUserId,
                    CreatedDate = DateTime.UtcNow
                };

                _context.Leads.Add(lead);
                await _context.SaveChangesAsync();

                // Add addresses
                foreach (var addressDto in createLeadDto.Addresses)
                {
                    var address = new LeadAddress
                    {
                        LeadId = lead.LeadId,
                        AddressType = addressDto.Type,
                        Address = addressDto.Address,
                        Pincode = addressDto.Pincode,
                        State = addressDto.State,
                        District = addressDto.District,
                        Landmark = addressDto.Landmark,
                        CreatedDate = DateTime.UtcNow
                    };

                    _context.LeadAddresses.Add(address);
                }

                // Add status history
                var statusHistory = new LeadStatusHistory
                {
                    LeadId = lead.LeadId,
                    Status = "new",
                    UpdatedBy = currentUserId,
                    Comments = "Lead created by admin",
                    Timestamp = DateTime.UtcNow
                };

                _context.LeadStatusHistory.Add(statusHistory);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // Return the created lead
                return await GetLead(lead.LeadId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating lead");
                return StatusCode(500, new { Message = "An error occurred while creating the lead." });
            }
        }

        [HttpPut("{id}/status")]
        [Authorize(Roles = "Admin,Supervisor,Agent")]
        public async Task<IActionResult> UpdateLeadStatus(int id, [FromBody] UpdateLeadStatusDto updateStatusDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

                var lead = await _context.Leads.FindAsync(id);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                // Role-based authorization
                if (currentUserRole == "Agent" && lead.AssignedTo != currentUserId)
                {
                    return Forbid("You can only update leads assigned to you");
                }

                using var transaction = await _context.Database.BeginTransactionAsync();

                // Update lead status
                lead.Status = updateStatusDto.Status;
                lead.ReviewComments = updateStatusDto.Comments;

                switch (updateStatusDto.Status)
                {
                    case "in-progress":
                        if (lead.StartedDate == null)
                            lead.StartedDate = DateTime.UtcNow;
                        break;
                    case "pending-review":
                        lead.SubmittedDate = DateTime.UtcNow;
                        break;
                    case "approved":
                        lead.ReviewedBy = currentUserId;
                        lead.ReviewedDate = DateTime.UtcNow;
                        lead.ApprovedDate = DateTime.UtcNow;
                        break;
                    case "rejected":
                        lead.ReviewedBy = currentUserId;
                        lead.ReviewedDate = DateTime.UtcNow;
                        lead.RejectedDate = DateTime.UtcNow;
                        lead.RejectionReason = updateStatusDto.RejectionReason;
                        break;
                }

                // Add status history
                var statusHistory = new LeadStatusHistory
                {
                    LeadId = id,
                    Status = updateStatusDto.Status,
                    UpdatedBy = currentUserId,
                    Comments = updateStatusDto.Comments ?? updateStatusDto.RejectionReason,
                    Timestamp = DateTime.UtcNow
                };

                _context.LeadStatusHistory.Add(statusHistory);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { Message = "Lead status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating lead status for lead {LeadId}", id);
                return StatusCode(500, new { Message = "An error occurred while updating the lead status." });
            }
        }

        [HttpPut("{id}/assign")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AssignLead(int id, [FromBody] AssignLeadDto assignLeadDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                var lead = await _context.Leads.FindAsync(id);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                // Verify agent exists and has Agent role
                var agent = await _context.Users.FindAsync(assignLeadDto.AgentId);
                if (agent == null || agent.Role != "Agent" || !agent.IsActive)
                {
                    return BadRequest(new { Message = "Invalid agent selected" });
                }

                using var transaction = await _context.Database.BeginTransactionAsync();

                // Update lead assignment
                lead.AssignedTo = assignLeadDto.AgentId;
                lead.AssignedDate = DateTime.UtcNow;
                lead.Status = "assigned";

                // Add status history
                var statusHistory = new LeadStatusHistory
                {
                    LeadId = id,
                    Status = "assigned",
                    UpdatedBy = currentUserId,
                    Comments = assignLeadDto.Comments ?? $"Lead assigned to {agent.FirstName} {agent.LastName}",
                    Timestamp = DateTime.UtcNow
                };

                _context.LeadStatusHistory.Add(statusHistory);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { Message = "Lead assigned successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning lead {LeadId}", id);
                return StatusCode(500, new { Message = "An error occurred while assigning the lead." });
            }
        }

        [HttpGet("dashboard-stats")]
        [Authorize]
        public async Task<ActionResult<DashboardStatsDto>> GetDashboardStats()
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

                var query = _context.Leads.AsQueryable();

                // Apply role-based filtering
                if (currentUserRole == "Agent")
                {
                    query = query.Where(l => l.AssignedTo == currentUserId);
                }

                var stats = await query
                    .GroupBy(l => 1)
                    .Select(g => new DashboardStatsDto
                    {
                        TotalLeads = g.Count(),
                        NewLeads = g.Count(l => l.Status == "new"),
                        AssignedLeads = g.Count(l => l.Status == "assigned"),
                        InProgressLeads = g.Count(l => l.Status == "in-progress"),
                        PendingReviewLeads = g.Count(l => l.Status == "pending-review"),
                        ApprovedLeads = g.Count(l => l.Status == "approved"),
                        RejectedLeads = g.Count(l => l.Status == "rejected")
                    })
                    .FirstOrDefaultAsync();

                return Ok(stats ?? new DashboardStatsDto());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard stats");
                return StatusCode(500, new { Message = "An error occurred while retrieving dashboard statistics." });
            }
        }
    }
}
