{"ast": null, "code": "import axios from 'axios';\n\n// Base API configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:59359';\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.api = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor to handle errors\n    this.api.interceptors.response.use(response => {\n      return response;\n    }, error => {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$config, _error$config2;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        // Token expired or invalid\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n      }\n\n      // Log error details for debugging\n      console.error('API Error:', {\n        status: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status,\n        statusText: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.statusText,\n        data: (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data,\n        url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n        method: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.method\n      });\n      return Promise.reject(error);\n    });\n  }\n\n  // Generic HTTP methods\n  async get(url, params) {\n    const response = await this.api.get(url, {\n      params\n    });\n    return response.data;\n  }\n  async post(url, data) {\n    const response = await this.api.post(url, data);\n    return response.data;\n  }\n  async put(url, data) {\n    const response = await this.api.put(url, data);\n    return response.data;\n  }\n  async delete(url) {\n    const response = await this.api.delete(url);\n    return response.data;\n  }\n\n  // File upload method\n  async uploadFile(url, formData) {\n    const response = await this.api.post(url, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  }\n\n  // Download file method\n  async downloadFile(url) {\n    const response = await this.api.get(url, {\n      responseType: 'blob'\n    });\n    return response.data;\n  }\n\n  // Health check\n  async healthCheck() {\n    return this.get('/health');\n  }\n}\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "api", "create", "baseURL", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$config", "_error$config2", "status", "removeItem", "window", "location", "href", "console", "statusText", "data", "url", "method", "get", "params", "post", "put", "delete", "uploadFile", "formData", "downloadFile", "responseType", "healthCheck", "apiService"], "sources": ["D:/Augment-projects/UBI-CPV-T/src/services/api.service.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\n\n// Base API configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:59359';\n\nclass ApiService {\n  private api: AxiosInstance;\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor to handle errors\n    this.api.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      (error: AxiosError) => {\n        if (error.response?.status === 401) {\n          // Token expired or invalid\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          localStorage.removeItem('user');\n          window.location.href = '/login';\n        }\n\n        // Log error details for debugging\n        console.error('API Error:', {\n          status: error.response?.status,\n          statusText: error.response?.statusText,\n          data: error.response?.data,\n          url: error.config?.url,\n          method: error.config?.method,\n        });\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Generic HTTP methods\n  async get<T>(url: string, params?: any): Promise<T> {\n    const response = await this.api.get<T>(url, { params });\n    return response.data;\n  }\n\n  async post<T>(url: string, data?: any): Promise<T> {\n    const response = await this.api.post<T>(url, data);\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any): Promise<T> {\n    const response = await this.api.put<T>(url, data);\n    return response.data;\n  }\n\n  async delete<T>(url: string): Promise<T> {\n    const response = await this.api.delete<T>(url);\n    return response.data;\n  }\n\n  // File upload method\n  async uploadFile<T>(url: string, formData: FormData): Promise<T> {\n    const response = await this.api.post<T>(url, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  }\n\n  // Download file method\n  async downloadFile(url: string): Promise<Blob> {\n    const response = await this.api.get(url, {\n      responseType: 'blob',\n    });\n    return response.data;\n  }\n\n  // Health check\n  async healthCheck(): Promise<{ status: string; timestamp: string; version: string }> {\n    return this.get('/health');\n  }\n}\n\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAoD,OAAO;;AAEvE;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,wBAAwB;AAE9E,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,GAAG;IAGT,IAAI,CAACA,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;MACtBC,OAAO,EAAER,YAAY;MACrBS,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAG;IAC1B;IACA,IAAI,CAACL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACL,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACd,GAAG,CAACM,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC/BS,QAAuB,IAAK;MAC3B,OAAOA,QAAQ;IACjB,CAAC,EACAH,KAAiB,IAAK;MAAA,IAAAI,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,cAAA;MACrB,IAAI,EAAAL,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBM,MAAM,MAAK,GAAG,EAAE;QAClC;QACAb,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;QAChCd,YAAY,CAACc,UAAU,CAAC,cAAc,CAAC;QACvCd,YAAY,CAACc,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;;MAEA;MACAC,OAAO,CAACf,KAAK,CAAC,YAAY,EAAE;QAC1BU,MAAM,GAAAL,gBAAA,GAAEL,KAAK,CAACG,QAAQ,cAAAE,gBAAA,uBAAdA,gBAAA,CAAgBK,MAAM;QAC9BM,UAAU,GAAAV,gBAAA,GAAEN,KAAK,CAACG,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBU,UAAU;QACtCC,IAAI,GAAAV,gBAAA,GAAEP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBU,IAAI;QAC1BC,GAAG,GAAAV,aAAA,GAAER,KAAK,CAACL,MAAM,cAAAa,aAAA,uBAAZA,aAAA,CAAcU,GAAG;QACtBC,MAAM,GAAAV,cAAA,GAAET,KAAK,CAACL,MAAM,cAAAc,cAAA,uBAAZA,cAAA,CAAcU;MACxB,CAAC,CAAC;MAEF,OAAOlB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAMoB,GAAGA,CAAIF,GAAW,EAAEG,MAAY,EAAc;IAClD,MAAMlB,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACkC,GAAG,CAAIF,GAAG,EAAE;MAAEG;IAAO,CAAC,CAAC;IACvD,OAAOlB,QAAQ,CAACc,IAAI;EACtB;EAEA,MAAMK,IAAIA,CAAIJ,GAAW,EAAED,IAAU,EAAc;IACjD,MAAMd,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACoC,IAAI,CAAIJ,GAAG,EAAED,IAAI,CAAC;IAClD,OAAOd,QAAQ,CAACc,IAAI;EACtB;EAEA,MAAMM,GAAGA,CAAIL,GAAW,EAAED,IAAU,EAAc;IAChD,MAAMd,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACqC,GAAG,CAAIL,GAAG,EAAED,IAAI,CAAC;IACjD,OAAOd,QAAQ,CAACc,IAAI;EACtB;EAEA,MAAMO,MAAMA,CAAIN,GAAW,EAAc;IACvC,MAAMf,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACsC,MAAM,CAAIN,GAAG,CAAC;IAC9C,OAAOf,QAAQ,CAACc,IAAI;EACtB;;EAEA;EACA,MAAMQ,UAAUA,CAAIP,GAAW,EAAEQ,QAAkB,EAAc;IAC/D,MAAMvB,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACoC,IAAI,CAAIJ,GAAG,EAAEQ,QAAQ,EAAE;MACrDpC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOa,QAAQ,CAACc,IAAI;EACtB;;EAEA;EACA,MAAMU,YAAYA,CAACT,GAAW,EAAiB;IAC7C,MAAMf,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACkC,GAAG,CAACF,GAAG,EAAE;MACvCU,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,OAAOzB,QAAQ,CAACc,IAAI;EACtB;;EAEA;EACA,MAAMY,WAAWA,CAAA,EAAoE;IACnF,OAAO,IAAI,CAACT,GAAG,CAAC,SAAS,CAAC;EAC5B;AACF;AAEA,OAAO,MAAMU,UAAU,GAAG,IAAI9C,UAAU,CAAC,CAAC;AAC1C,eAAe8C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}