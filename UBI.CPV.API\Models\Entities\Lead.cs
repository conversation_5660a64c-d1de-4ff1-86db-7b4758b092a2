using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace UBI.CPV.API.Models.Entities
{
    public class Lead
    {
        [Key]
        public int LeadId { get; set; }

        [Required]
        [StringLength(100)]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        public string MobileNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LoanType { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "new";

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Required]
        public int CreatedBy { get; set; }

        public int? AssignedTo { get; set; }

        public DateTime? AssignedDate { get; set; }

        public DateTime? StartedDate { get; set; }

        public DateTime? SubmittedDate { get; set; }

        public DateTime? ReviewedDate { get; set; }

        public DateTime? ApprovedDate { get; set; }

        public DateTime? RejectedDate { get; set; }

        public int? ReviewedBy { get; set; }

        [StringLength(500)]
        public string? RejectionReason { get; set; }

        [StringLength(1000)]
        public string? ReviewComments { get; set; }

        // Navigation properties
        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("AssignedTo")]
        public virtual User? AssignedAgent { get; set; }

        [ForeignKey("ReviewedBy")]
        public virtual User? Reviewer { get; set; }

        public virtual ICollection<LeadAddress> Addresses { get; set; } = new List<LeadAddress>();
        public virtual ICollection<LeadStatusHistory> StatusHistory { get; set; } = new List<LeadStatusHistory>();
        public virtual ICollection<LeadDocument> Documents { get; set; } = new List<LeadDocument>();
        public virtual ICollection<CroppedImage> CroppedImages { get; set; } = new List<CroppedImage>();
        public virtual ICollection<VerificationDocument> VerificationDocuments { get; set; } = new List<VerificationDocument>();
        public virtual VerificationData? VerificationData { get; set; }
    }

    public class LeadAddress
    {
        [Key]
        public int AddressId { get; set; }

        [Required]
        public int LeadId { get; set; }

        [Required]
        [StringLength(50)]
        public string AddressType { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string Pincode { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string State { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string District { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Landmark { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("LeadId")]
        public virtual Lead Lead { get; set; } = null!;
    }

    public class LeadStatusHistory
    {
        [Key]
        public int HistoryId { get; set; }

        [Required]
        public int LeadId { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = string.Empty;

        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [Required]
        public int UpdatedBy { get; set; }

        [StringLength(500)]
        public string? Comments { get; set; }

        // Navigation properties
        [ForeignKey("LeadId")]
        public virtual Lead Lead { get; set; } = null!;

        [ForeignKey("UpdatedBy")]
        public virtual User UpdatedByUser { get; set; } = null!;
    }
}
