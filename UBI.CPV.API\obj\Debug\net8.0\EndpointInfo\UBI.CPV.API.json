{"openapi": "3.0.1", "info": {"title": "UBI-CPV API", "description": "Union Bank of India - Customer Premises Verification API\n\nThis API provides endpoints for managing customer verification processes, including lead management, document uploads, and verification workflows.", "contact": {"name": "UBI-CPV Support", "email": "<EMAIL>"}, "license": {"name": "UBI Internal Use", "url": "https://www.unionbankofindia.co.in"}, "version": "v1.0.0"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "User Login", "description": "Authenticates a user with username, password, and role. Returns JWT token on successful authentication.", "operationId": "<PERSON><PERSON>", "requestBody": {"description": "Login credentials including username, password, and role", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}}}, "responses": {"200": {"description": "Login response", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}, "500": {"description": "Internal server error"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "User <PERSON>", "description": "Invalidates the current user session and logs out the user.", "operationId": "Logout", "responses": {"200": {"description": "Logout successful"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Refresh JWT Token", "description": "Generates a new JWT token using a valid refresh token.", "operationId": "RefreshToken", "requestBody": {"description": "Refresh token request containing the current token and refresh token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "Token refresh response", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}, "500": {"description": "Internal server error"}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get Current User", "description": "Retrieves the information of the currently authenticated user.", "operationId": "GetCurrentUser", "responses": {"200": {"description": "User information", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "User not found"}, "500": {"description": "Internal server error"}}}}, "/api/Documents/leads/{leadId}/upload": {"post": {"tags": ["Documents"], "parameters": [{"name": "leadId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["DocumentTypeId", "File"], "type": "object", "properties": {"File": {"type": "string", "format": "binary"}, "DocumentTypeId": {"type": "integer", "format": "int32"}, "DocumentCategory": {"type": "string"}}}, "encoding": {"File": {"style": "form"}, "DocumentTypeId": {"style": "form"}, "DocumentCategory": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}}}}}}, "/api/Documents/leads/{leadId}/cropped-images": {"post": {"tags": ["Documents"], "parameters": [{"name": "leadId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["File"], "type": "object", "properties": {"File": {"type": "string", "format": "binary"}, "CropData": {"type": "string"}, "PageNumber": {"type": "integer", "format": "int32"}}}, "encoding": {"File": {"style": "form"}, "CropData": {"style": "form"}, "PageNumber": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}}}}}}, "/api/Documents/leads/{leadId}/verification-documents": {"post": {"tags": ["Documents"], "parameters": [{"name": "leadId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["DocumentCategory", "DocumentTypeId", "File"], "type": "object", "properties": {"File": {"type": "string", "format": "binary"}, "DocumentTypeId": {"type": "integer", "format": "int32"}, "DocumentCategory": {"type": "string"}}}, "encoding": {"File": {"style": "form"}, "DocumentTypeId": {"style": "form"}, "DocumentCategory": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadResultDto"}}}}}}, "get": {"tags": ["Documents"], "parameters": [{"name": "leadId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VerificationDocumentDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VerificationDocumentDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VerificationDocumentDto"}}}}}}}}, "/api/Documents/types": {"get": {"tags": ["Documents"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentTypeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentTypeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentTypeDto"}}}}}}}}, "/api/Leads": {"get": {"tags": ["Leads"], "summary": "Get Leads", "description": "Retrieves a paginated list of leads. Agents can only see leads assigned to them.", "operationId": "GetLeads", "parameters": [{"name": "pageNumber", "in": "query", "description": "Page number (default: 1)", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "Number of items per page (default: 10)", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "description": "Filter by lead status (optional)", "schema": {"type": "string"}}, {"name": "assignedTo", "in": "query", "description": "Filter by assigned agent ID (optional)", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Paginated list of leads", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LeadListDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LeadListDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LeadListDtoPagedResultDto"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}, "post": {"tags": ["Leads"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLeadDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateLeadDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateLeadDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LeadDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LeadDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LeadDto"}}}}}}}, "/api/Leads/{id}": {"get": {"tags": ["Leads"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LeadDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LeadDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LeadDto"}}}}}}}, "/api/Leads/{id}/status": {"put": {"tags": ["Leads"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLeadStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateLeadStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateLeadStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Leads/{id}/assign": {"put": {"tags": ["Leads"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignLeadDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignLeadDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignLeadDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Leads/dashboard-stats": {"get": {"tags": ["Leads"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DashboardStatsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DashboardStatsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DashboardStatsDto"}}}}}}}, "/health": {"get": {"tags": ["UBI.CPV.API"], "responses": {"200": {"description": "Success"}}}}, "/api/Verification/leads/{leadId}": {"post": {"tags": ["Verification"], "parameters": [{"name": "leadId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVerificationDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateVerificationDataDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateVerificationDataDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}}}}}, "put": {"tags": ["Verification"], "parameters": [{"name": "leadId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVerificationDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateVerificationDataDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateVerificationDataDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}}}}}, "get": {"tags": ["Verification"], "parameters": [{"name": "leadId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationDataDto"}}}}}}}, "/api/Verification/agent/dashboard-stats": {"get": {"tags": ["Verification"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentDashboardStatsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentDashboardStatsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentDashboardStatsDto"}}}}}}}, "/api/Verification/supervisor/dashboard-stats": {"get": {"tags": ["Verification"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SupervisorDashboardStatsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SupervisorDashboardStatsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SupervisorDashboardStatsDto"}}}}}}}}, "components": {"schemas": {"AddressDto": {"type": "object", "properties": {"addressId": {"type": "integer", "format": "int32"}, "addressType": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "pincode": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "landmark": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AgentDashboardStatsDto": {"type": "object", "properties": {"pendingLeads": {"type": "integer", "format": "int32"}, "inProgressLeads": {"type": "integer", "format": "int32"}, "completedLeads": {"type": "integer", "format": "int32"}, "rejectedLeads": {"type": "integer", "format": "int32"}, "totalAssigned": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AgentPerformanceDto": {"type": "object", "properties": {"agentId": {"type": "integer", "format": "int32"}, "agentName": {"type": "string", "nullable": true}, "assignedCount": {"type": "integer", "format": "int32"}, "completedCount": {"type": "integer", "format": "int32"}, "approvedCount": {"type": "integer", "format": "int32"}, "rejectedCount": {"type": "integer", "format": "int32"}, "completionRate": {"type": "number", "format": "double"}, "approvalRate": {"type": "number", "format": "double"}}, "additionalProperties": false}, "AssignLeadDto": {"required": ["agentId"], "type": "object", "properties": {"agentId": {"type": "integer", "format": "int32"}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateAddressDto": {"required": ["address", "district", "pincode", "state", "type"], "type": "object", "properties": {"type": {"minLength": 1, "type": "string"}, "address": {"maxLength": 500, "minLength": 0, "type": "string"}, "pincode": {"maxLength": 10, "minLength": 0, "type": "string"}, "state": {"maxLength": 50, "minLength": 0, "type": "string"}, "district": {"maxLength": 50, "minLength": 0, "type": "string"}, "landmark": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "CreateLeadDto": {"required": ["addresses", "customerName", "loanType", "mobileNumber"], "type": "object", "properties": {"customerName": {"maxLength": 100, "minLength": 0, "type": "string"}, "mobileNumber": {"maxLength": 15, "minLength": 0, "pattern": "^\\d{10}$", "type": "string"}, "loanType": {"minLength": 1, "type": "string"}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/CreateAddressDto"}}}, "additionalProperties": false}, "CreateVerificationDataDto": {"required": ["agentContact", "<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"agentName": {"maxLength": 100, "minLength": 0, "type": "string"}, "agentContact": {"maxLength": 15, "minLength": 0, "pattern": "^\\d{10}$", "type": "string"}, "addressConfirmed": {"type": "string", "nullable": true}, "personMet": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "relationship": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "officeAddress": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "officeState": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "officeDistrict": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "officePincode": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "landmark": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "companyType": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "businessNature": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "establishmentYear": {"maximum": 2100, "minimum": 1900, "type": "integer", "format": "int32", "nullable": true}, "employeesCount": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "grossSalary": {"maximum": *********.99, "minimum": 0, "type": "number", "format": "double", "nullable": true}, "netSalary": {"maximum": *********.99, "minimum": 0, "type": "number", "format": "double", "nullable": true}, "proofType": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "additionalNotes": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "CroppedImageDto": {"type": "object", "properties": {"imageId": {"type": "integer", "format": "int32"}, "leadId": {"type": "integer", "format": "int32"}, "fileName": {"type": "string", "nullable": true}, "originalFileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "mimeType": {"type": "string", "nullable": true}, "cropData": {"type": "string", "nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "createdByName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DashboardStatsDto": {"type": "object", "properties": {"totalLeads": {"type": "integer", "format": "int32"}, "newLeads": {"type": "integer", "format": "int32"}, "assignedLeads": {"type": "integer", "format": "int32"}, "inProgressLeads": {"type": "integer", "format": "int32"}, "pendingReviewLeads": {"type": "integer", "format": "int32"}, "approvedLeads": {"type": "integer", "format": "int32"}, "rejectedLeads": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DocumentDto": {"type": "object", "properties": {"documentId": {"type": "integer", "format": "int32"}, "leadId": {"type": "integer", "format": "int32"}, "documentTypeName": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "originalFileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "mimeType": {"type": "string", "nullable": true}, "uploadedDate": {"type": "string", "format": "date-time"}, "uploadedByName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "DocumentTypeDto": {"type": "object", "properties": {"documentTypeId": {"type": "integer", "format": "int32"}, "typeName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "FileUploadResultDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "documentId": {"type": "integer", "format": "int32", "nullable": true}, "imageId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "LeadDto": {"type": "object", "properties": {"leadId": {"type": "integer", "format": "int32"}, "customerName": {"type": "string", "nullable": true}, "mobileNumber": {"type": "string", "nullable": true}, "loanType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "assignedDate": {"type": "string", "format": "date-time", "nullable": true}, "startedDate": {"type": "string", "format": "date-time", "nullable": true}, "submittedDate": {"type": "string", "format": "date-time", "nullable": true}, "reviewedDate": {"type": "string", "format": "date-time", "nullable": true}, "approvedDate": {"type": "string", "format": "date-time", "nullable": true}, "rejectedDate": {"type": "string", "format": "date-time", "nullable": true}, "rejectionReason": {"type": "string", "nullable": true}, "reviewComments": {"type": "string", "nullable": true}, "creator": {"$ref": "#/components/schemas/UserDto"}, "assignedAgent": {"$ref": "#/components/schemas/UserDto"}, "reviewer": {"$ref": "#/components/schemas/UserDto"}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDto"}, "nullable": true}, "statusHistory": {"type": "array", "items": {"$ref": "#/components/schemas/StatusHistoryDto"}, "nullable": true}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentDto"}, "nullable": true}, "croppedImages": {"type": "array", "items": {"$ref": "#/components/schemas/CroppedImageDto"}, "nullable": true}, "verificationData": {"$ref": "#/components/schemas/VerificationDataDto"}}, "additionalProperties": false}, "LeadListDto": {"type": "object", "properties": {"leadId": {"type": "integer", "format": "int32"}, "customerName": {"type": "string", "nullable": true}, "mobileNumber": {"type": "string", "nullable": true}, "loanType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time"}, "assignedDate": {"type": "string", "format": "date-time", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "assignedToName": {"type": "string", "nullable": true}, "reviewedByName": {"type": "string", "nullable": true}, "documentCount": {"type": "integer", "format": "int32"}, "croppedImageCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LeadListDtoPagedResultDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LeadListDto"}, "nullable": true}, "totalRecords": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "additionalProperties": false}, "LoginRequestDto": {"required": ["password", "role", "username"], "type": "object", "properties": {"username": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}, "role": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LoginResponseDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "user": {"$ref": "#/components/schemas/UserDto"}, "expiresAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "RefreshTokenDto": {"required": ["refreshToken", "token"], "type": "object", "properties": {"token": {"minLength": 1, "type": "string"}, "refreshToken": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "StatusHistoryDto": {"type": "object", "properties": {"historyId": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "comments": {"type": "string", "nullable": true}, "updatedByUser": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "SupervisorDashboardStatsDto": {"type": "object", "properties": {"pendingReviews": {"type": "integer", "format": "int32"}, "approvedToday": {"type": "integer", "format": "int32"}, "rejectedToday": {"type": "integer", "format": "int32"}, "totalReviewed": {"type": "integer", "format": "int32"}, "approvalRate": {"type": "number", "format": "double"}, "agentPerformance": {"type": "array", "items": {"$ref": "#/components/schemas/AgentPerformanceDto"}, "nullable": true}}, "additionalProperties": false}, "UpdateLeadStatusDto": {"required": ["status"], "type": "object", "properties": {"status": {"minLength": 1, "type": "string"}, "comments": {"type": "string", "nullable": true}, "rejectionReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateVerificationDataDto": {"required": ["agentContact", "<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"agentName": {"maxLength": 100, "minLength": 0, "type": "string"}, "agentContact": {"maxLength": 15, "minLength": 0, "pattern": "^\\d{10}$", "type": "string"}, "addressConfirmed": {"type": "string", "nullable": true}, "personMet": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "relationship": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "officeAddress": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "officeState": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "officeDistrict": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "officePincode": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "landmark": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "companyType": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "businessNature": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "establishmentYear": {"maximum": 2100, "minimum": 1900, "type": "integer", "format": "int32", "nullable": true}, "employeesCount": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "grossSalary": {"maximum": *********.99, "minimum": 0, "type": "number", "format": "double", "nullable": true}, "netSalary": {"maximum": *********.99, "minimum": 0, "type": "number", "format": "double", "nullable": true}, "proofType": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "additionalNotes": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdDate": {"type": "string", "format": "date-time"}, "lastLoginDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VerificationDataDto": {"type": "object", "properties": {"verificationId": {"type": "integer", "format": "int32"}, "leadId": {"type": "integer", "format": "int32"}, "agentName": {"type": "string", "nullable": true}, "agentContact": {"type": "string", "nullable": true}, "addressConfirmed": {"type": "string", "nullable": true}, "personMet": {"type": "string", "nullable": true}, "relationship": {"type": "string", "nullable": true}, "officeAddress": {"type": "string", "nullable": true}, "officeState": {"type": "string", "nullable": true}, "officeDistrict": {"type": "string", "nullable": true}, "officePincode": {"type": "string", "nullable": true}, "landmark": {"type": "string", "nullable": true}, "companyType": {"type": "string", "nullable": true}, "businessNature": {"type": "string", "nullable": true}, "establishmentYear": {"type": "integer", "format": "int32", "nullable": true}, "employeesCount": {"type": "string", "nullable": true}, "grossSalary": {"type": "number", "format": "double", "nullable": true}, "netSalary": {"type": "number", "format": "double", "nullable": true}, "proofType": {"type": "string", "nullable": true}, "verificationDate": {"type": "string", "format": "date-time"}, "additionalNotes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerificationDocumentDto": {"type": "object", "properties": {"documentId": {"type": "integer", "format": "int32"}, "leadId": {"type": "integer", "format": "int32"}, "documentTypeName": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "originalFileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "mimeType": {"type": "string", "nullable": true}, "uploadedDate": {"type": "string", "format": "date-time"}, "uploadedByName": {"type": "string", "nullable": true}, "documentCategory": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bear<PERSON> scheme.\n\nEnter 'Bearer' [space] and then your token in the text input below.\n\nExample: 'Bearer 12345abcdef'", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}], "tags": [{"name": "<PERSON><PERSON>", "description": "Authentication endpoints for user login, logout, and token management"}, {"name": "Leads", "description": "Lead management endpoints for customer verification processes"}]}