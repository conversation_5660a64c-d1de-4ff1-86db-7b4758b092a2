import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  MenuItem,
  IconButton,
  Alert,
  <PERSON>,
  CardContent,
  CardHeader,
} from '@mui/material';
import {
  ArrowBack,
  Add,
  Delete,
  Save,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { leadService } from '../../services/lead.service';
import { CreateLead, CreateAddress, LoanType, AddressType } from '../../types/lead.types';
import LoadingSpinner from '../../components/common/LoadingSpinner';

interface CreateLeadFormData {
  customerName: string;
  mobileNumber: string;
  loanType: LoanType;
  addresses: CreateAddressFormData[];
}

interface CreateAddressFormData {
  type: string;
  addressLine1: string;
  addressLine2?: string;
  district: string;
  state: string;
  pincode: string;
  landmark?: string;
}

const CreateLeadPage: React.FC = () => {
  const navigate = useNavigate();
  const { hasRole } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Check if user has Admin role
  React.useEffect(() => {
    if (!hasRole('Admin')) {
      navigate('/leads');
    }
  }, [hasRole, navigate]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateLeadFormData>({
    defaultValues: {
      customerName: '',
      mobileNumber: '',
      loanType: 'Home Loan',
      addresses: [
        {
          type: 'Residential',
          addressLine1: '',
          addressLine2: '',
          district: '',
          state: '',
          pincode: '',
          landmark: '',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'addresses',
  });

  const onSubmit = async (data: CreateLeadFormData) => {
    try {
      setLoading(true);
      setError('');

      // Transform form data to API format
      const createLeadData: CreateLead = {
        customerName: data.customerName,
        mobileNumber: data.mobileNumber,
        loanType: data.loanType,
        addresses: data.addresses.map(addr => ({
          type: addr.type,
          address: addr.addressLine2
            ? `${addr.addressLine1}, ${addr.addressLine2}`
            : addr.addressLine1,
          pincode: addr.pincode,
          state: addr.state,
          district: addr.district,
          landmark: addr.landmark || undefined,
        })),
      };

      console.log('Sending lead data:', createLeadData);
      const newLead = await leadService.createLead(createLeadData);
      navigate(`/leads/${newLead.leadId}`);
    } catch (err: any) {
      console.error('Create lead error:', err);
      console.error('Error response:', err.response);

      let errorMessage = 'Failed to create lead';
      if (err.response?.status === 403) {
        errorMessage = 'Access denied. Only Admin users can create leads.';
      } else if (err.response?.status === 400) {
        errorMessage = err.response?.data?.message || 'Invalid data provided. Please check all fields.';
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/leads');
  };

  const addAddress = () => {
    append({
      type: 'Residential',
      addressLine1: '',
      addressLine2: '',
      district: '',
      state: '',
      pincode: '',
      landmark: '',
    });
  };

  const removeAddress = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const loanTypes: { value: LoanType; label: string }[] = [
    { value: 'Home Loan', label: 'Home Loan' },
    { value: 'Personal Loan', label: 'Personal Loan' },
    { value: 'Business Loan', label: 'Business Loan' },
    { value: 'Car Loan', label: 'Car Loan' },
    { value: 'Education Loan', label: 'Education Loan' },
  ];

  const addressTypes: { value: AddressType; label: string }[] = [
    { value: 'Residential', label: 'Residential' },
    { value: 'Office', label: 'Office' },
    { value: 'Business', label: 'Business' },
  ];

  if (loading) {
    return <LoadingSpinner message="Creating lead..." />;
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={handleBack} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Box>
          <Typography variant="h4" gutterBottom>
            Create New Lead
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            Enter customer details and verification addresses
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Customer Information */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Customer Information
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Controller
                name="customerName"
                control={control}
                rules={{ required: 'Customer name is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Customer Name"
                    error={!!errors.customerName}
                    helperText={errors.customerName?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="mobileNumber"
                control={control}
                rules={{
                  required: 'Mobile number is required',
                  pattern: {
                    value: /^\d{10}$/,
                    message: 'Mobile number must be 10 digits',
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Mobile Number"
                    error={!!errors.mobileNumber}
                    helperText={errors.mobileNumber?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="loanType"
                control={control}
                rules={{ required: 'Loan type is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    select
                    label="Loan Type"
                    error={!!errors.loanType}
                    helperText={errors.loanType?.message}
                  >
                    {loanTypes.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Addresses */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              Addresses
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={addAddress}
            >
              Add Address
            </Button>
          </Box>

          {fields.map((field, index) => (
            <Card key={field.id} sx={{ mb: 2 }}>
              <CardHeader
                title={`Address ${index + 1}`}
                action={
                  fields.length > 1 && (
                    <IconButton
                      onClick={() => removeAddress(index)}
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  )
                }
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name={`addresses.${index}.type`}
                      control={control}
                      rules={{ required: 'Address type is required' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          select
                          label="Address Type"
                          error={!!errors.addresses?.[index]?.type}
                          helperText={errors.addresses?.[index]?.type?.message as string}
                        >
                          {addressTypes.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </TextField>
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name={`addresses.${index}.addressLine1`}
                      control={control}
                      rules={{ required: 'Address line 1 is required' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Address Line 1"
                          error={!!errors.addresses?.[index]?.addressLine1}
                          helperText={errors.addresses?.[index]?.addressLine1?.message as string}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name={`addresses.${index}.addressLine2`}
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Address Line 2 (Optional)"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name={`addresses.${index}.district`}
                      control={control}
                      rules={{ required: 'District is required' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="District"
                          error={!!errors.addresses?.[index]?.district}
                          helperText={errors.addresses?.[index]?.district?.message as string}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name={`addresses.${index}.state`}
                      control={control}
                      rules={{ required: 'State is required' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="State"
                          error={!!errors.addresses?.[index]?.state}
                          helperText={errors.addresses?.[index]?.state?.message as string}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name={`addresses.${index}.pincode`}
                      control={control}
                      rules={{
                        required: 'Pincode is required',
                        pattern: {
                          value: /^\d{6}$/,
                          message: 'Pincode must be 6 digits',
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Pincode"
                          error={!!errors.addresses?.[index]?.pincode}
                          helperText={errors.addresses?.[index]?.pincode?.message as string}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Controller
                      name={`addresses.${index}.landmark`}
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Landmark (Optional)"
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          ))}
        </Paper>

        {/* Actions */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            onClick={handleBack}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
          >
            Create Lead
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default CreateLeadPage;
