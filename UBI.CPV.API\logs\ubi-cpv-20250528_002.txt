2025-05-28 12:25:15.670 +05:30 [ERR] An error occurred while creating the database or seeding data
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 156
2025-05-28 12:25:15.891 +05:30 [INF] UBI-CPV API starting up...
2025-05-28 12:25:16.111 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-28 12:25:16.735 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-28 12:25:16.751 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-28 12:25:16.773 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-28 12:25:16.786 +05:30 [INF] Hosting environment: Development
2025-05-28 12:25:16.799 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
[2025-05-28 12:26:26.832 +05:30 INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal'; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.248 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "DocumentTypes" (
    "DocumentTypeId" INTEGER NOT NULL CONSTRAINT "PK_DocumentTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "Description" TEXT NULL,
    "IsActive" INTEGER NOT NULL
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.262 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "UserId" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "Salt" TEXT NOT NULL,
    "Role" TEXT NOT NULL,
    "FirstName" TEXT NOT NULL,
    "LastName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "IsActive" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "LastLoginDate" TEXT NULL,
    "CreatedBy" INTEGER NULL,
    CONSTRAINT "FK_Users_Users_CreatedBy" FOREIGN KEY ("CreatedBy") REFERENCES "Users" ("UserId") ON DELETE RESTRICT
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.278 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AppSettings" (
    "SettingId" INTEGER NOT NULL CONSTRAINT "PK_AppSettings" PRIMARY KEY AUTOINCREMENT,
    "SettingKey" TEXT NOT NULL,
    "SettingValue" TEXT NOT NULL,
    "Description" TEXT NULL,
    "LastUpdated" TEXT NOT NULL,
    "UpdatedBy" INTEGER NULL,
    CONSTRAINT "FK_AppSettings_Users_UpdatedBy" FOREIGN KEY ("UpdatedBy") REFERENCES "Users" ("UserId") ON DELETE SET NULL
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.292 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AuditLogs" (
    "LogId" INTEGER NOT NULL CONSTRAINT "PK_AuditLogs" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NULL,
    "Action" TEXT NOT NULL,
    "TableName" TEXT NULL,
    "RecordId" INTEGER NULL,
    "OldValues" TEXT NULL,
    "NewValues" TEXT NULL,
    "Timestamp" TEXT NOT NULL,
    "IPAddress" TEXT NULL,
    "UserAgent" TEXT NULL,
    CONSTRAINT "FK_AuditLogs_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("UserId") ON DELETE SET NULL
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.304 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Leads" (
    "LeadId" INTEGER NOT NULL CONSTRAINT "PK_Leads" PRIMARY KEY AUTOINCREMENT,
    "CustomerName" TEXT NOT NULL,
    "MobileNumber" TEXT NOT NULL,
    "LoanType" TEXT NOT NULL,
    "Status" TEXT NOT NULL DEFAULT 'new',
    "CreatedDate" TEXT NOT NULL DEFAULT (datetime('now')),
    "CreatedBy" INTEGER NOT NULL,
    "AssignedTo" INTEGER NULL,
    "AssignedDate" TEXT NULL,
    "StartedDate" TEXT NULL,
    "SubmittedDate" TEXT NULL,
    "ReviewedDate" TEXT NULL,
    "ApprovedDate" TEXT NULL,
    "RejectedDate" TEXT NULL,
    "ReviewedBy" INTEGER NULL,
    "RejectionReason" TEXT NULL,
    "ReviewComments" TEXT NULL,
    CONSTRAINT "FK_Leads_Users_AssignedTo" FOREIGN KEY ("AssignedTo") REFERENCES "Users" ("UserId") ON DELETE RESTRICT,
    CONSTRAINT "FK_Leads_Users_CreatedBy" FOREIGN KEY ("CreatedBy") REFERENCES "Users" ("UserId") ON DELETE RESTRICT,
    CONSTRAINT "FK_Leads_Users_ReviewedBy" FOREIGN KEY ("ReviewedBy") REFERENCES "Users" ("UserId") ON DELETE RESTRICT
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.320 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserSessions" (
    "SessionId" INTEGER NOT NULL CONSTRAINT "PK_UserSessions" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "Token" TEXT NOT NULL,
    "RefreshToken" TEXT NULL,
    "ExpiresAt" TEXT NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    CONSTRAINT "FK_UserSessions_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("UserId") ON DELETE CASCADE
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.333 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CroppedImages" (
    "ImageId" INTEGER NOT NULL CONSTRAINT "PK_CroppedImages" PRIMARY KEY AUTOINCREMENT,
    "LeadId" INTEGER NOT NULL,
    "FileName" TEXT NOT NULL,
    "OriginalFileName" TEXT NOT NULL,
    "FilePath" TEXT NOT NULL,
    "FileSize" INTEGER NOT NULL,
    "MimeType" TEXT NOT NULL,
    "CropData" TEXT NULL,
    "PageNumber" INTEGER NULL,
    "CreatedDate" TEXT NOT NULL,
    "CreatedBy" INTEGER NOT NULL,
    CONSTRAINT "FK_CroppedImages_Leads_LeadId" FOREIGN KEY ("LeadId") REFERENCES "Leads" ("LeadId") ON DELETE CASCADE,
    CONSTRAINT "FK_CroppedImages_Users_CreatedBy" FOREIGN KEY ("CreatedBy") REFERENCES "Users" ("UserId") ON DELETE RESTRICT
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.346 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "LeadAddresses" (
    "AddressId" INTEGER NOT NULL CONSTRAINT "PK_LeadAddresses" PRIMARY KEY AUTOINCREMENT,
    "LeadId" INTEGER NOT NULL,
    "AddressType" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "Pincode" TEXT NOT NULL,
    "State" TEXT NOT NULL,
    "District" TEXT NOT NULL,
    "Landmark" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    CONSTRAINT "FK_LeadAddresses_Leads_LeadId" FOREIGN KEY ("LeadId") REFERENCES "Leads" ("LeadId") ON DELETE CASCADE
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.360 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "LeadDocuments" (
    "DocumentId" INTEGER NOT NULL CONSTRAINT "PK_LeadDocuments" PRIMARY KEY AUTOINCREMENT,
    "LeadId" INTEGER NOT NULL,
    "DocumentTypeId" INTEGER NOT NULL,
    "FileName" TEXT NOT NULL,
    "OriginalFileName" TEXT NOT NULL,
    "FilePath" TEXT NOT NULL,
    "FileSize" INTEGER NOT NULL,
    "MimeType" TEXT NOT NULL,
    "UploadedDate" TEXT NOT NULL,
    "UploadedBy" INTEGER NOT NULL,
    "IsActive" INTEGER NOT NULL,
    CONSTRAINT "FK_LeadDocuments_DocumentTypes_DocumentTypeId" FOREIGN KEY ("DocumentTypeId") REFERENCES "DocumentTypes" ("DocumentTypeId") ON DELETE RESTRICT,
    CONSTRAINT "FK_LeadDocuments_Leads_LeadId" FOREIGN KEY ("LeadId") REFERENCES "Leads" ("LeadId") ON DELETE CASCADE,
    CONSTRAINT "FK_LeadDocuments_Users_UploadedBy" FOREIGN KEY ("UploadedBy") REFERENCES "Users" ("UserId") ON DELETE RESTRICT
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.373 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "LeadStatusHistory" (
    "HistoryId" INTEGER NOT NULL CONSTRAINT "PK_LeadStatusHistory" PRIMARY KEY AUTOINCREMENT,
    "LeadId" INTEGER NOT NULL,
    "Status" TEXT NOT NULL,
    "Timestamp" TEXT NOT NULL,
    "UpdatedBy" INTEGER NOT NULL,
    "Comments" TEXT NULL,
    CONSTRAINT "FK_LeadStatusHistory_Leads_LeadId" FOREIGN KEY ("LeadId") REFERENCES "Leads" ("LeadId") ON DELETE CASCADE,
    CONSTRAINT "FK_LeadStatusHistory_Users_UpdatedBy" FOREIGN KEY ("UpdatedBy") REFERENCES "Users" ("UserId") ON DELETE RESTRICT
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.387 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "VerificationData" (
    "VerificationId" INTEGER NOT NULL CONSTRAINT "PK_VerificationData" PRIMARY KEY AUTOINCREMENT,
    "LeadId" INTEGER NOT NULL,
    "AgentName" TEXT NOT NULL,
    "AgentContact" TEXT NOT NULL,
    "AddressConfirmed" TEXT NULL,
    "PersonMet" TEXT NULL,
    "Relationship" TEXT NULL,
    "OfficeAddress" TEXT NULL,
    "OfficeState" TEXT NULL,
    "OfficeDistrict" TEXT NULL,
    "OfficePincode" TEXT NULL,
    "Landmark" TEXT NULL,
    "CompanyType" TEXT NULL,
    "BusinessNature" TEXT NULL,
    "EstablishmentYear" INTEGER NULL,
    "EmployeesCount" TEXT NULL,
    "GrossSalary" decimal(15,2) NULL,
    "NetSalary" decimal(15,2) NULL,
    "ProofType" TEXT NULL,
    "VerificationDate" TEXT NOT NULL,
    "AdditionalNotes" TEXT NULL,
    CONSTRAINT "FK_VerificationData_Leads_LeadId" FOREIGN KEY ("LeadId") REFERENCES "Leads" ("LeadId") ON DELETE CASCADE
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.402 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "VerificationDocuments" (
    "DocumentId" INTEGER NOT NULL CONSTRAINT "PK_VerificationDocuments" PRIMARY KEY AUTOINCREMENT,
    "LeadId" INTEGER NOT NULL,
    "DocumentTypeId" INTEGER NOT NULL,
    "FileName" TEXT NOT NULL,
    "OriginalFileName" TEXT NOT NULL,
    "FilePath" TEXT NOT NULL,
    "FileSize" INTEGER NOT NULL,
    "MimeType" TEXT NOT NULL,
    "UploadedDate" TEXT NOT NULL,
    "UploadedBy" INTEGER NOT NULL,
    "DocumentCategory" TEXT NULL,
    "IsActive" INTEGER NOT NULL,
    CONSTRAINT "FK_VerificationDocuments_DocumentTypes_DocumentTypeId" FOREIGN KEY ("DocumentTypeId") REFERENCES "DocumentTypes" ("DocumentTypeId") ON DELETE RESTRICT,
    CONSTRAINT "FK_VerificationDocuments_Leads_LeadId" FOREIGN KEY ("LeadId") REFERENCES "Leads" ("LeadId") ON DELETE CASCADE,
    CONSTRAINT "FK_VerificationDocuments_Users_UploadedBy" FOREIGN KEY ("UploadedBy") REFERENCES "Users" ("UserId") ON DELETE RESTRICT
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.443 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AppSettings" ("SettingId", "Description", "LastUpdated", "SettingKey", "SettingValue", "UpdatedBy")
VALUES (1, 'Maximum file upload size in bytes (25MB)', '2025-05-28 06:56:27.0196471', 'MAX_FILE_SIZE', '26214400', NULL);
SELECT changes();

INSERT INTO "AppSettings" ("SettingId", "Description", "LastUpdated", "SettingKey", "SettingValue", "UpdatedBy")
VALUES (2, 'Allowed file extensions for upload', '2025-05-28 06:56:27.0196477', 'ALLOWED_FILE_TYPES', 'pdf,jpg,jpeg,png,gif', NULL);
SELECT changes();

INSERT INTO "AppSettings" ("SettingId", "Description", "LastUpdated", "SettingKey", "SettingValue", "UpdatedBy")
VALUES (3, 'Session timeout in minutes', '2025-05-28 06:56:27.019648', 'SESSION_TIMEOUT', '480', NULL);
SELECT changes();

INSERT INTO "AppSettings" ("SettingId", "Description", "LastUpdated", "SettingKey", "SettingValue", "UpdatedBy")
VALUES (4, 'Minimum password length', '2025-05-28 06:56:27.0196481', 'PASSWORD_MIN_LENGTH', '8', NULL);
SELECT changes();

INSERT INTO "AppSettings" ("SettingId", "Description", "LastUpdated", "SettingKey", "SettingValue", "UpdatedBy")
VALUES (5, 'Enable email notifications', '2025-05-28 06:56:27.0196483', 'ENABLE_EMAIL_NOTIFICATIONS', 'true', NULL);
SELECT changes(); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.458 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "DocumentTypes" ("DocumentTypeId", "Description", "IsActive", "TypeName")
VALUES (1, 'Main PDF document uploaded during lead creation', 1, 'PDF_DOCUMENT');
SELECT changes();

INSERT INTO "DocumentTypes" ("DocumentTypeId", "Description", "IsActive", "TypeName")
VALUES (2, 'KYC documents like Aadhaar, PAN, etc.', 1, 'KYC_DOCUMENT');
SELECT changes();

INSERT INTO "DocumentTypes" ("DocumentTypeId", "Description", "IsActive", "TypeName")
VALUES (3, 'Applicant and premises photos', 1, 'PHOTO');
SELECT changes();

INSERT INTO "DocumentTypes" ("DocumentTypeId", "Description", "IsActive", "TypeName")
VALUES (4, 'Address proof documents', 1, 'ADDRESS_PROOF');
SELECT changes();

INSERT INTO "DocumentTypes" ("DocumentTypeId", "Description", "IsActive", "TypeName")
VALUES (5, 'Salary slips, bank statements, etc.', 1, 'INCOME_PROOF');
SELECT changes();

INSERT INTO "DocumentTypes" ("DocumentTypeId", "Description", "IsActive", "TypeName")
VALUES (6, 'Any additional supporting documents', 1, 'ADDITIONAL');
SELECT changes(); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.470 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_AppSettings_SettingKey" ON "AppSettings" ("SettingKey"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.482 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AppSettings_UpdatedBy" ON "AppSettings" ("UpdatedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.493 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AuditLogs_UserId" ON "AuditLogs" ("UserId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.503 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CroppedImages_CreatedBy" ON "CroppedImages" ("CreatedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.516 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CroppedImages_LeadId" ON "CroppedImages" ("LeadId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.528 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_DocumentTypes_TypeName" ON "DocumentTypes" ("TypeName"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.538 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_LeadAddresses_LeadId" ON "LeadAddresses" ("LeadId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.550 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_LeadDocuments_DocumentTypeId" ON "LeadDocuments" ("DocumentTypeId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.564 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_LeadDocuments_LeadId" ON "LeadDocuments" ("LeadId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.574 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_LeadDocuments_UploadedBy" ON "LeadDocuments" ("UploadedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.647 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Leads_AssignedTo" ON "Leads" ("AssignedTo"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.664 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Leads_CreatedBy" ON "Leads" ("CreatedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.680 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Leads_CreatedDate" ON "Leads" ("CreatedDate"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.691 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Leads_CustomerName" ON "Leads" ("CustomerName"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.702 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Leads_MobileNumber" ON "Leads" ("MobileNumber"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.718 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Leads_ReviewedBy" ON "Leads" ("ReviewedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.733 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Leads_Status" ON "Leads" ("Status"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.750 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_LeadStatusHistory_LeadId" ON "LeadStatusHistory" ("LeadId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.770 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_LeadStatusHistory_Timestamp" ON "LeadStatusHistory" ("Timestamp"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.793 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_LeadStatusHistory_UpdatedBy" ON "LeadStatusHistory" ("UpdatedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.820 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Users_CreatedBy" ON "Users" ("CreatedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.842 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.854 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.867 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserSessions_Token" ON "UserSessions" ("Token"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.881 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserSessions_UserId" ON "UserSessions" ("UserId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.895 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_VerificationData_LeadId" ON "VerificationData" ("LeadId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.905 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_VerificationDocuments_DocumentTypeId" ON "VerificationDocuments" ("DocumentTypeId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.918 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_VerificationDocuments_LeadId" ON "VerificationDocuments" ("LeadId"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:27.930 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_VerificationDocuments_UploadedBy" ON "VerificationDocuments" ("UploadedBy"); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:28.119 +05:30 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS "u") {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:29.121 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime), @p2='?' (Size = 13), @p3='?' (Size = 6), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (Size = 13), @p7='?' (Size = 60), @p8='?' (Size = 10), @p9='?' (Size = 5), @p10='?' (Size = 10), @p11='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("CreatedBy", "CreatedDate", "Email", "FirstName", "IsActive", "LastLoginDate", "LastName", "PasswordHash", "PhoneNumber", "Role", "Salt", "Username")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11)
RETURNING "UserId"; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:29.141 +05:30 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime), @p2='?' (Size = 13), @p3='?' (Size = 4), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (Size = 5), @p7='?' (Size = 60), @p8='?' (Size = 10), @p9='?' (Size = 5), @p10='?' (Size = 10), @p11='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("CreatedBy", "CreatedDate", "Email", "FirstName", "IsActive", "LastLoginDate", "LastName", "PasswordHash", "PhoneNumber", "Role", "Salt", "Username")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11)
RETURNING "UserId"; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:29.165 +05:30 INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime), @p2='?' (Size = 18), @p3='?' (Size = 4), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (Size = 10), @p7='?' (Size = 60), @p8='?' (Size = 10), @p9='?' (Size = 10), @p10='?' (Size = 15), @p11='?' (Size = 10)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("CreatedBy", "CreatedDate", "Email", "FirstName", "IsActive", "LastLoginDate", "LastName", "PasswordHash", "PhoneNumber", "Role", "Salt", "Username")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11)
RETURNING "UserId"; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-28 12:26:29.199 +05:30 INF] Default users created successfully {}
[2025-05-28 12:26:29.209 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-28 12:26:29.279 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-28 12:26:29.537 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:26:29.546 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:26:29.554 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:26:29.558 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:26:29.563 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:56:29.934 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCTN3RCGIHF:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNCTN3RCGIHF"}
[2025-05-28 12:56:30.049 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCTN3RCGIHF:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNCTN3RCGIHF"}
[2025-05-28 12:56:30.068 +05:30 INF] Request origin http://localhost:3000 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCTN3RCGIHF:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNCTN3RCGIHF"}
[2025-05-28 12:56:30.154 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 223.1508ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCTN3RCGIHF:00000001","RequestPath":"/api/auth/login","ConnectionId":"0HNCTN3RCGIHF"}
