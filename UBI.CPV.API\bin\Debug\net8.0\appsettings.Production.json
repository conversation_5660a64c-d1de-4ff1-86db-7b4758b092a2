{"ConnectionStrings": {"DefaultConnection": "Data Source=MBSDEVGGN63729A;Initial Catalog=UBI_CPV_DB;User ID=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true;Encrypt=false;"}, "Jwt": {"Key": "UBI-CPV-Super-Secret-JWT-Key-For-Production-Environment-2024-Change-This-In-Production", "Issuer": "UBI-CPV-API", "Audience": "UBI-CPV-Client", "ExpiryHours": 8}, "FileUpload": {"MaxFileSizeBytes": 26214400, "AllowedExtensions": [".pdf", ".jpg", ".jpeg", ".png", ".gif"], "UploadPath": "wwwroot/uploads"}, "Cors": {"AllowedOrigins": ["https://your-production-domain.com", "https://ubi-cpv.yourdomain.com"]}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Error", "System": "Error", "Microsoft.EntityFrameworkCore": "Error", "UBI.CPV.API": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/var/log/ubi-cpv/ubi-cpv-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:5000"}, "Https": {"Url": "https://0.0.0.0:5001", "Certificate": {"Path": "/etc/ssl/certs/ubi-cpv.pfx", "Password": "your-certificate-password"}}}}}