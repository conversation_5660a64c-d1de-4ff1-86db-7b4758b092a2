2025-05-29 18:37:52.262 +05:30 [ERR] An error occurred while creating the database or seeding data
Microsoft.Data.SqlClient.SqlException (0x80131904): A connection was successfully established with the server, but then an error occurred during the login process. (provider: SSL Provider, error: 0 - The certificate chain was issued by an authority that is not trusted.)
 ---> System.ComponentModel.Win32Exception (0x80090325): The certificate chain was issued by an authority that is not trusted.
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ThrowExceptionAndWarning(Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.SNIWritePacket(PacketHandle packet, UInt32& sniError, Boolean canAccumulate, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.WriteSni(Boolean canAccumulate)
   at Microsoft.Data.SqlClient.TdsParserStateObject.WritePacket(Byte flushMode, Boolean canAccumulate)
   at Microsoft.Data.SqlClient.TdsParser.TdsLogin(SqlLogin rec, FeatureExtension requestedFeatures, SessionData recoverySessionData, FederatedAuthenticationFeatureExtensionData fedAuthFeatureExtensionData, SqlConnectionEncryptOption encrypt)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.Login(ServerInfo server, TimeoutTimer timeout, String newPassword, SecureString newSecurePassword, SqlConnectionEncryptOption encrypt)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.AttemptOneLogin(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, Boolean ignoreSniOpenTimeout, TimeoutTimer timeout, Boolean withFailover)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.LoginNoFailover(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString connectionOptions, SqlCredential credential, TimeoutTimer timeout)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.OpenLoginEnlist(TimeoutTimer timeout, SqlConnectionString connectionOptions, SqlCredential credential, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken, DbConnectionPool pool)
   at Microsoft.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.<>c__DisplayClass18_0.<Exists>b__0(DateTime giveUp)
   at Microsoft.EntityFrameworkCore.ExecutionStrategyExtensions.<>c__DisplayClass12_0`2.<Execute>b__0(DbContext _, TState s)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ExecutionStrategyExtensions.Execute[TState,TResult](IExecutionStrategy strategy, TState state, Func`2 operation, Func`2 verifySucceeded)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.Exists(Boolean retryOnNotExists)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.Exists()
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:f167ca7f-6943-4208-a608-fbdb9cace923
Error Number:-2146893019,State:0,Class:20
2025-05-29 18:37:52.382 +05:30 [INF] UBI-CPV API starting up...
2025-05-29 18:37:52.415 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 18:37:52.641 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-29 18:37:52.652 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-29 18:37:52.671 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 18:37:52.680 +05:30 [INF] Hosting environment: Development
2025-05-29 18:37:52.688 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
[2025-05-29 18:38:55.728 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:55.856 +05:30 INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:55.877 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:56.040 +05:30 INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [__EFMigrationsHistory] (
    [MigrationId] nvarchar(150) NOT NULL,
    [ProductVersion] nvarchar(32) NOT NULL,
    CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:56.052 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:56.070 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:56.121 +05:30 INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId]; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:56.153 +05:30 INF] Applying migration '20250529130711_InitialCreate'. {"EventId":{"Id":20402,"Name":"Microsoft.EntityFrameworkCore.Migrations.MigrationApplying"},"SourceContext":"Microsoft.EntityFrameworkCore.Migrations"}
[2025-05-29 18:38:56.291 +05:30 ERR] Failed executing DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
); {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:38:56.308 +05:30 ERR] An error occurred while creating the database or seeding data {}
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:78b9cce1-bfd3-43c0-9e96-c8d4ed1db502
Error Number:2714,State:6,Class:16
[2025-05-29 18:38:56.340 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-29 18:38:56.375 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-29 18:38:56.640 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:38:56.649 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:38:56.660 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:38:56.665 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:38:56.671 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:39:21.220 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N49:00000001","RequestPath":"/swagger","ConnectionId":"0HNCUM83C0N49"}
[2025-05-29 18:39:21.325 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger - 301 0 null 106.1584ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N49:00000001","RequestPath":"/swagger","ConnectionId":"0HNCUM83C0N49"}
[2025-05-29 18:39:21.353 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N49:00000002","RequestPath":"/swagger/index.html","ConnectionId":"0HNCUM83C0N49"}
[2025-05-29 18:39:21.421 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 68.0438ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N49:00000002","RequestPath":"/swagger/index.html","ConnectionId":"0HNCUM83C0N49"}
[2025-05-29 18:39:21.499 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N49:00000003","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCUM83C0N49"}
[2025-05-29 18:39:21.559 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4D:00000001","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCUM83C0N4D"}
[2025-05-29 18:39:21.559 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000001","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:21.567 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4F:00000001","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCUM83C0N4F"}
[2025-05-29 18:39:21.572 +05:30 INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUM83C0N49:00000003","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCUM83C0N49"}
[2025-05-29 18:39:21.624 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 124.8693ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N49:00000003","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCUM83C0N49"}
[2025-05-29 18:39:21.629 +05:30 INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUM83C0N4D:00000001","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCUM83C0N4D"}
[2025-05-29 18:39:21.631 +05:30 INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUM83C0N4F:00000001","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCUM83C0N4F"}
[2025-05-29 18:39:21.639 +05:30 INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUM83C0N4E:00000001","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:21.674 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 114.9409ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4D:00000001","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCUM83C0N4D"}
[2025-05-29 18:39:21.676 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 108.9907ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4F:00000001","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCUM83C0N4F"}
[2025-05-29 18:39:21.678 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 118.7596ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000001","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:21.914 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000002","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:22.094 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 179.9137ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000002","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:51.748 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000003","RequestPath":"/swagger/index.html","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:51.765 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 17.3231ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000003","RequestPath":"/swagger/index.html","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:51.945 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000004","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUM83C0N4E"}
[2025-05-29 18:39:51.952 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4F:00000002","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCUM83C0N4F"}
[2025-05-29 18:39:52.009 +05:30 INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUM83C0N4F:00000002","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCUM83C0N4F"}
[2025-05-29 18:39:52.031 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 79.2948ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4F:00000002","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCUM83C0N4F"}
[2025-05-29 18:39:52.109 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 163.3942ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUM83C0N4E:00000004","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUM83C0N4E"}
