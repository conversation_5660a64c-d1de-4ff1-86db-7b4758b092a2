using System.ComponentModel.DataAnnotations;

namespace UBI.CPV.API.Models.DTOs
{
    public class CreateLeadDto
    {
        [Required]
        [StringLength(100)]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        [RegularExpression(@"^\d{10}$", ErrorMessage = "Mobile number must be 10 digits")]
        public string MobileNumber { get; set; } = string.Empty;

        [Required]
        public string LoanType { get; set; } = string.Empty;

        [Required]
        public List<CreateAddressDto> Addresses { get; set; } = new List<CreateAddressDto>();
    }

    public class CreateAddressDto
    {
        [Required]
        public string Type { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string Pincode { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string State { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string District { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Landmark { get; set; }
    }

    public class LeadDto
    {
        public int LeadId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string LoanType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime? AssignedDate { get; set; }
        public DateTime? StartedDate { get; set; }
        public DateTime? SubmittedDate { get; set; }
        public DateTime? ReviewedDate { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public DateTime? RejectedDate { get; set; }
        public string? RejectionReason { get; set; }
        public string? ReviewComments { get; set; }
        public UserDto? Creator { get; set; }
        public UserDto? AssignedAgent { get; set; }
        public UserDto? Reviewer { get; set; }
        public List<AddressDto> Addresses { get; set; } = new List<AddressDto>();
        public List<StatusHistoryDto> StatusHistory { get; set; } = new List<StatusHistoryDto>();
        public List<DocumentDto> Documents { get; set; } = new List<DocumentDto>();
        public List<CroppedImageDto> CroppedImages { get; set; } = new List<CroppedImageDto>();
        public VerificationDataDto? VerificationData { get; set; }
    }

    public class AddressDto
    {
        public int AddressId { get; set; }
        public string AddressType { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string Pincode { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string District { get; set; } = string.Empty;
        public string? Landmark { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class StatusHistoryDto
    {
        public int HistoryId { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? Comments { get; set; }
        public UserDto? UpdatedByUser { get; set; }
    }

    public class UpdateLeadStatusDto
    {
        [Required]
        public string Status { get; set; } = string.Empty;

        public string? Comments { get; set; }

        public string? RejectionReason { get; set; }
    }

    public class AssignLeadDto
    {
        [Required]
        public int AgentId { get; set; }

        public string? Comments { get; set; }
    }

    public class LeadListDto
    {
        public int LeadId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string LoanType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime? AssignedDate { get; set; }
        public string? CreatedByName { get; set; }
        public string? AssignedToName { get; set; }
        public string? ReviewedByName { get; set; }
        public int DocumentCount { get; set; }
        public int CroppedImageCount { get; set; }
    }

    public class DashboardStatsDto
    {
        public int TotalLeads { get; set; }
        public int NewLeads { get; set; }
        public int AssignedLeads { get; set; }
        public int InProgressLeads { get; set; }
        public int PendingReviewLeads { get; set; }
        public int ApprovedLeads { get; set; }
        public int RejectedLeads { get; set; }
    }

    public class PagedResultDto<T>
    {
        public List<T> Data { get; set; } = new List<T>();
        public int TotalRecords { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
}
