﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using UBI.CPV.API.Data;

#nullable disable

namespace UBI.CPV.API.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250529130711_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.AppSetting", b =>
                {
                    b.Property<int>("SettingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettingId"));

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<string>("SettingKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SettingValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("SettingId");

                    b.HasIndex("SettingKey")
                        .IsUnique();

                    b.HasIndex("UpdatedBy");

                    b.ToTable("AppSettings");

                    b.HasData(
                        new
                        {
                            SettingId = 1,
                            Description = "Maximum file upload size in bytes (25MB)",
                            LastUpdated = new DateTime(2025, 5, 29, 13, 7, 10, 465, DateTimeKind.Utc).AddTicks(7326),
                            SettingKey = "MAX_FILE_SIZE",
                            SettingValue = "26214400"
                        },
                        new
                        {
                            SettingId = 2,
                            Description = "Allowed file extensions for upload",
                            LastUpdated = new DateTime(2025, 5, 29, 13, 7, 10, 465, DateTimeKind.Utc).AddTicks(7332),
                            SettingKey = "ALLOWED_FILE_TYPES",
                            SettingValue = "pdf,jpg,jpeg,png,gif"
                        },
                        new
                        {
                            SettingId = 3,
                            Description = "Session timeout in minutes",
                            LastUpdated = new DateTime(2025, 5, 29, 13, 7, 10, 465, DateTimeKind.Utc).AddTicks(7334),
                            SettingKey = "SESSION_TIMEOUT",
                            SettingValue = "480"
                        },
                        new
                        {
                            SettingId = 4,
                            Description = "Minimum password length",
                            LastUpdated = new DateTime(2025, 5, 29, 13, 7, 10, 465, DateTimeKind.Utc).AddTicks(7335),
                            SettingKey = "PASSWORD_MIN_LENGTH",
                            SettingValue = "8"
                        },
                        new
                        {
                            SettingId = 5,
                            Description = "Enable email notifications",
                            LastUpdated = new DateTime(2025, 5, 29, 13, 7, 10, 465, DateTimeKind.Utc).AddTicks(7337),
                            SettingKey = "ENABLE_EMAIL_NOTIFICATIONS",
                            SettingValue = "true"
                        });
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.AuditLog", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LogId"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RecordId")
                        .HasColumnType("int");

                    b.Property<string>("TableName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LogId");

                    b.HasIndex("UserId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.CroppedImage", b =>
                {
                    b.Property<int>("ImageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ImageId"));

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CropData")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<int>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("PageNumber")
                        .HasColumnType("int");

                    b.HasKey("ImageId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LeadId");

                    b.ToTable("CroppedImages");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.DocumentType", b =>
                {
                    b.Property<int>("DocumentTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DocumentTypeId"));

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("DocumentTypeId");

                    b.HasIndex("TypeName")
                        .IsUnique();

                    b.ToTable("DocumentTypes");

                    b.HasData(
                        new
                        {
                            DocumentTypeId = 1,
                            Description = "Main PDF document uploaded during lead creation",
                            IsActive = true,
                            TypeName = "PDF_DOCUMENT"
                        },
                        new
                        {
                            DocumentTypeId = 2,
                            Description = "KYC documents like Aadhaar, PAN, etc.",
                            IsActive = true,
                            TypeName = "KYC_DOCUMENT"
                        },
                        new
                        {
                            DocumentTypeId = 3,
                            Description = "Applicant and premises photos",
                            IsActive = true,
                            TypeName = "PHOTO"
                        },
                        new
                        {
                            DocumentTypeId = 4,
                            Description = "Address proof documents",
                            IsActive = true,
                            TypeName = "ADDRESS_PROOF"
                        },
                        new
                        {
                            DocumentTypeId = 5,
                            Description = "Salary slips, bank statements, etc.",
                            IsActive = true,
                            TypeName = "INCOME_PROOF"
                        },
                        new
                        {
                            DocumentTypeId = 6,
                            Description = "Any additional supporting documents",
                            IsActive = true,
                            TypeName = "ADDITIONAL"
                        });
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.Lead", b =>
                {
                    b.Property<int>("LeadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LeadId"));

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("AssignedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("AssignedTo")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LoanType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MobileNumber")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime?>("RejectedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ReviewComments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int?>("ReviewedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StartedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("new");

                    b.Property<DateTime?>("SubmittedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("LeadId");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("CustomerName");

                    b.HasIndex("MobileNumber");

                    b.HasIndex("ReviewedBy");

                    b.HasIndex("Status");

                    b.ToTable("Leads");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.LeadAddress", b =>
                {
                    b.Property<int>("AddressId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AddressId"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("AddressType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("District")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Landmark")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("Pincode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("AddressId");

                    b.HasIndex("LeadId");

                    b.ToTable("LeadAddresses");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.LeadDocument", b =>
                {
                    b.Property<int>("DocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DocumentId"));

                    b.Property<int>("DocumentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("UploadedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("UploadedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("DocumentId");

                    b.HasIndex("DocumentTypeId");

                    b.HasIndex("LeadId");

                    b.HasIndex("UploadedBy");

                    b.ToTable("LeadDocuments");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.LeadStatusHistory", b =>
                {
                    b.Property<int>("HistoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("HistoryId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<int>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("HistoryId");

                    b.HasIndex("LeadId");

                    b.HasIndex("Timestamp");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("LeadStatusHistory");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserId"));

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Salt")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("UserId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.UserSession", b =>
                {
                    b.Property<int>("SessionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SessionId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("SessionId");

                    b.HasIndex("Token");

                    b.HasIndex("UserId");

                    b.ToTable("UserSessions");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.VerificationData", b =>
                {
                    b.Property<int>("VerificationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("VerificationId"));

                    b.Property<string>("AdditionalNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("AddressConfirmed")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("AgentContact")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("AgentName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BusinessNature")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CompanyType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EmployeesCount")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("EstablishmentYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("GrossSalary")
                        .HasColumnType("decimal(15,2)");

                    b.Property<string>("Landmark")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("LeadId")
                        .HasColumnType("int");

                    b.Property<decimal?>("NetSalary")
                        .HasColumnType("decimal(15,2)");

                    b.Property<string>("OfficeAddress")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("OfficeDistrict")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("OfficePincode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("OfficeState")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PersonMet")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ProofType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Relationship")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("VerificationDate")
                        .HasColumnType("datetime2");

                    b.HasKey("VerificationId");

                    b.HasIndex("LeadId")
                        .IsUnique();

                    b.ToTable("VerificationData");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.VerificationDocument", b =>
                {
                    b.Property<int>("DocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DocumentId"));

                    b.Property<string>("DocumentCategory")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("DocumentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("UploadedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("UploadedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("DocumentId");

                    b.HasIndex("DocumentTypeId");

                    b.HasIndex("LeadId");

                    b.HasIndex("UploadedBy");

                    b.ToTable("VerificationDocuments");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.AppSetting", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.AuditLog", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.CroppedImage", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.User", "CreatedByUser")
                        .WithMany("CroppedImages")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("UBI.CPV.API.Models.Entities.Lead", "Lead")
                        .WithMany("CroppedImages")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("Lead");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.Lead", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.User", "AssignedAgent")
                        .WithMany("AssignedLeads")
                        .HasForeignKey("AssignedTo")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("UBI.CPV.API.Models.Entities.User", "Creator")
                        .WithMany("CreatedLeads")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("UBI.CPV.API.Models.Entities.User", "Reviewer")
                        .WithMany("ReviewedLeads")
                        .HasForeignKey("ReviewedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AssignedAgent");

                    b.Navigation("Creator");

                    b.Navigation("Reviewer");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.LeadAddress", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.Lead", "Lead")
                        .WithMany("Addresses")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Lead");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.LeadDocument", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.DocumentType", "DocumentType")
                        .WithMany("LeadDocuments")
                        .HasForeignKey("DocumentTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("UBI.CPV.API.Models.Entities.Lead", "Lead")
                        .WithMany("Documents")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBI.CPV.API.Models.Entities.User", "UploadedByUser")
                        .WithMany("UploadedDocuments")
                        .HasForeignKey("UploadedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DocumentType");

                    b.Navigation("Lead");

                    b.Navigation("UploadedByUser");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.LeadStatusHistory", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.Lead", "Lead")
                        .WithMany("StatusHistory")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBI.CPV.API.Models.Entities.User", "UpdatedByUser")
                        .WithMany("StatusHistories")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Lead");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.User", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.User", "Creator")
                        .WithMany("CreatedUsers")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Creator");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.UserSession", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.User", "User")
                        .WithMany("Sessions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.VerificationData", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.Lead", "Lead")
                        .WithOne("VerificationData")
                        .HasForeignKey("UBI.CPV.API.Models.Entities.VerificationData", "LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Lead");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.VerificationDocument", b =>
                {
                    b.HasOne("UBI.CPV.API.Models.Entities.DocumentType", "DocumentType")
                        .WithMany("VerificationDocuments")
                        .HasForeignKey("DocumentTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("UBI.CPV.API.Models.Entities.Lead", "Lead")
                        .WithMany("VerificationDocuments")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBI.CPV.API.Models.Entities.User", "UploadedByUser")
                        .WithMany("VerificationDocuments")
                        .HasForeignKey("UploadedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DocumentType");

                    b.Navigation("Lead");

                    b.Navigation("UploadedByUser");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.DocumentType", b =>
                {
                    b.Navigation("LeadDocuments");

                    b.Navigation("VerificationDocuments");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.Lead", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("CroppedImages");

                    b.Navigation("Documents");

                    b.Navigation("StatusHistory");

                    b.Navigation("VerificationData");

                    b.Navigation("VerificationDocuments");
                });

            modelBuilder.Entity("UBI.CPV.API.Models.Entities.User", b =>
                {
                    b.Navigation("AssignedLeads");

                    b.Navigation("CreatedLeads");

                    b.Navigation("CreatedUsers");

                    b.Navigation("CroppedImages");

                    b.Navigation("ReviewedLeads");

                    b.Navigation("Sessions");

                    b.Navigation("StatusHistories");

                    b.Navigation("UploadedDocuments");

                    b.Navigation("VerificationDocuments");
                });
#pragma warning restore 612, 618
        }
    }
}
