using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using UBI.CPV.API.Data;
using UBI.CPV.API.Models.DTOs;
using UBI.CPV.API.Models.Entities;

namespace UBI.CPV.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DocumentsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DocumentsController> _logger;
        private readonly IWebHostEnvironment _environment;

        public DocumentsController(ApplicationDbContext context, ILogger<DocumentsController> logger, IWebHostEnvironment environment)
        {
            _context = context;
            _logger = logger;
            _environment = environment;
        }

        [HttpPost("leads/{leadId}/upload")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<FileUploadResultDto>> UploadLeadDocument(int leadId, [FromForm] UploadDocumentDto uploadDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                // Verify lead exists
                var lead = await _context.Leads.FindAsync(leadId);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                // Verify document type exists
                var documentType = await _context.DocumentTypes.FindAsync(uploadDto.DocumentTypeId);
                if (documentType == null)
                {
                    return BadRequest(new { Message = "Invalid document type" });
                }

                // Validate file
                if (uploadDto.File == null || uploadDto.File.Length == 0)
                {
                    return BadRequest(new { Message = "No file uploaded" });
                }

                // Check file size (25MB limit)
                if (uploadDto.File.Length > 26214400)
                {
                    return BadRequest(new { Message = "File size exceeds 25MB limit" });
                }

                // Check file type
                var allowedExtensions = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(uploadDto.File.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { Message = "File type not allowed" });
                }

                // Create uploads directory if it doesn't exist
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                }

                // Generate unique filename
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                // Save file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await uploadDto.File.CopyToAsync(stream);
                }

                // Save document record
                var document = new LeadDocument
                {
                    LeadId = leadId,
                    DocumentTypeId = uploadDto.DocumentTypeId,
                    FileName = fileName,
                    OriginalFileName = uploadDto.File.FileName,
                    FilePath = $"/uploads/{fileName}",
                    FileSize = uploadDto.File.Length,
                    MimeType = uploadDto.File.ContentType,
                    UploadedBy = currentUserId,
                    UploadedDate = DateTime.UtcNow,
                    IsActive = true
                };

                _context.LeadDocuments.Add(document);
                await _context.SaveChangesAsync();

                return Ok(new FileUploadResultDto
                {
                    Success = true,
                    Message = "File uploaded successfully",
                    FileName = fileName,
                    FilePath = document.FilePath,
                    FileSize = uploadDto.File.Length,
                    DocumentId = document.DocumentId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading document for lead {LeadId}", leadId);
                return StatusCode(500, new FileUploadResultDto
                {
                    Success = false,
                    Message = "An error occurred while uploading the file."
                });
            }
        }

        [HttpPost("leads/{leadId}/cropped-images")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<FileUploadResultDto>> UploadCroppedImage(int leadId, [FromForm] UploadCroppedImageDto uploadDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                // Verify lead exists
                var lead = await _context.Leads.FindAsync(leadId);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                // Validate file
                if (uploadDto.File == null || uploadDto.File.Length == 0)
                {
                    return BadRequest(new { Message = "No file uploaded" });
                }

                // Check file type (only images for cropped images)
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(uploadDto.File.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { Message = "Only image files are allowed for cropped images" });
                }

                // Create uploads directory if it doesn't exist
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "cropped");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                }

                // Generate unique filename
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                // Save file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await uploadDto.File.CopyToAsync(stream);
                }

                // Save cropped image record
                var croppedImage = new CroppedImage
                {
                    LeadId = leadId,
                    FileName = fileName,
                    OriginalFileName = uploadDto.File.FileName,
                    FilePath = $"/uploads/cropped/{fileName}",
                    FileSize = uploadDto.File.Length,
                    MimeType = uploadDto.File.ContentType,
                    CropData = uploadDto.CropData,
                    PageNumber = uploadDto.PageNumber,
                    CreatedBy = currentUserId,
                    CreatedDate = DateTime.UtcNow
                };

                _context.CroppedImages.Add(croppedImage);
                await _context.SaveChangesAsync();

                return Ok(new FileUploadResultDto
                {
                    Success = true,
                    Message = "Cropped image uploaded successfully",
                    FileName = fileName,
                    FilePath = croppedImage.FilePath,
                    FileSize = uploadDto.File.Length,
                    ImageId = croppedImage.ImageId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading cropped image for lead {LeadId}", leadId);
                return StatusCode(500, new FileUploadResultDto
                {
                    Success = false,
                    Message = "An error occurred while uploading the cropped image."
                });
            }
        }

        [HttpPost("leads/{leadId}/verification-documents")]
        [Authorize(Roles = "Agent")]
        public async Task<ActionResult<FileUploadResultDto>> UploadVerificationDocument(int leadId, [FromForm] UploadVerificationDocumentDto uploadDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                // Verify lead exists and is assigned to current agent
                var lead = await _context.Leads.FindAsync(leadId);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                if (lead.AssignedTo != currentUserId)
                {
                    return Forbid("You can only upload documents for leads assigned to you");
                }

                // Verify document type exists
                var documentType = await _context.DocumentTypes.FindAsync(uploadDto.DocumentTypeId);
                if (documentType == null)
                {
                    return BadRequest(new { Message = "Invalid document type" });
                }

                // Validate file
                if (uploadDto.File == null || uploadDto.File.Length == 0)
                {
                    return BadRequest(new { Message = "No file uploaded" });
                }

                // Check file size (25MB limit)
                if (uploadDto.File.Length > 26214400)
                {
                    return BadRequest(new { Message = "File size exceeds 25MB limit" });
                }

                // Check file type
                var allowedExtensions = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(uploadDto.File.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { Message = "File type not allowed" });
                }

                // Create uploads directory if it doesn't exist
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "verification");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                }

                // Generate unique filename
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                // Save file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await uploadDto.File.CopyToAsync(stream);
                }

                // Save verification document record
                var document = new VerificationDocument
                {
                    LeadId = leadId,
                    DocumentTypeId = uploadDto.DocumentTypeId,
                    FileName = fileName,
                    OriginalFileName = uploadDto.File.FileName,
                    FilePath = $"/uploads/verification/{fileName}",
                    FileSize = uploadDto.File.Length,
                    MimeType = uploadDto.File.ContentType,
                    DocumentCategory = uploadDto.DocumentCategory,
                    UploadedBy = currentUserId,
                    UploadedDate = DateTime.UtcNow,
                    IsActive = true
                };

                _context.VerificationDocuments.Add(document);
                await _context.SaveChangesAsync();

                return Ok(new FileUploadResultDto
                {
                    Success = true,
                    Message = "Verification document uploaded successfully",
                    FileName = fileName,
                    FilePath = document.FilePath,
                    FileSize = uploadDto.File.Length,
                    DocumentId = document.DocumentId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading verification document for lead {LeadId}", leadId);
                return StatusCode(500, new FileUploadResultDto
                {
                    Success = false,
                    Message = "An error occurred while uploading the verification document."
                });
            }
        }

        [HttpGet("types")]
        public async Task<ActionResult<List<DocumentTypeDto>>> GetDocumentTypes()
        {
            try
            {
                var documentTypes = await _context.DocumentTypes
                    .Where(dt => dt.IsActive)
                    .Select(dt => new DocumentTypeDto
                    {
                        DocumentTypeId = dt.DocumentTypeId,
                        TypeName = dt.TypeName,
                        Description = dt.Description,
                        IsActive = dt.IsActive
                    })
                    .ToListAsync();

                return Ok(documentTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting document types");
                return StatusCode(500, new { Message = "An error occurred while retrieving document types." });
            }
        }

        [HttpGet("leads/{leadId}/verification-documents")]
        [Authorize]
        public async Task<ActionResult<List<VerificationDocumentDto>>> GetVerificationDocuments(int leadId)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

                // Verify lead access
                var lead = await _context.Leads.FindAsync(leadId);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                // Role-based authorization
                if (currentUserRole == "Agent" && lead.AssignedTo != currentUserId)
                {
                    return Forbid("You can only access documents for leads assigned to you");
                }

                var documents = await _context.VerificationDocuments
                    .Include(vd => vd.DocumentType)
                    .Include(vd => vd.UploadedByUser)
                    .Where(vd => vd.LeadId == leadId && vd.IsActive)
                    .Select(vd => new VerificationDocumentDto
                    {
                        DocumentId = vd.DocumentId,
                        LeadId = vd.LeadId,
                        DocumentTypeName = vd.DocumentType.TypeName,
                        FileName = vd.FileName,
                        OriginalFileName = vd.OriginalFileName,
                        FilePath = vd.FilePath,
                        FileSize = vd.FileSize,
                        MimeType = vd.MimeType,
                        UploadedDate = vd.UploadedDate,
                        UploadedByName = vd.UploadedByUser != null ? $"{vd.UploadedByUser.FirstName} {vd.UploadedByUser.LastName}" : null,
                        DocumentCategory = vd.DocumentCategory,
                        IsActive = vd.IsActive
                    })
                    .ToListAsync();

                return Ok(documents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verification documents for lead {LeadId}", leadId);
                return StatusCode(500, new { Message = "An error occurred while retrieving verification documents." });
            }
        }
    }
}
