[{"D:\\Augment-projects\\UBI-CPV-T\\src\\index.tsx": "1", "D:\\Augment-projects\\UBI-CPV-T\\src\\reportWebVitals.ts": "2", "D:\\Augment-projects\\UBI-CPV-T\\src\\App.tsx": "3", "D:\\Augment-projects\\UBI-CPV-T\\src\\theme\\theme.ts": "4", "D:\\Augment-projects\\UBI-CPV-T\\src\\contexts\\AuthContext.tsx": "5", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\ProtectedRoute.tsx": "6", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Layout.tsx": "7", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadsPage.tsx": "8", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\documents\\DocumentsPage.tsx": "9", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\auth\\LoginPage.tsx": "10", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\dashboard\\DashboardPage.tsx": "11", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadDetailsPage.tsx": "12", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\CreateLeadPage.tsx": "13", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\common\\NotFoundPage.tsx": "14", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\verification\\VerificationPage.tsx": "15", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\profile\\ProfilePage.tsx": "16", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Sidebar.tsx": "17", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\lead.service.ts": "18", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Header.tsx": "19", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\auth.service.ts": "20", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\StatusChip.tsx": "21", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\LoadingSpinner.tsx": "22", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\verification.service.ts": "23", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\api.service.ts": "24", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\CreateLeadPageSimple.tsx": "25"}, {"size": 554, "mtime": 1749120636486, "results": "26", "hashOfConfig": "27"}, {"size": 425, "mtime": 1749120656082, "results": "28", "hashOfConfig": "27"}, {"size": 2536, "mtime": 1749124691275, "results": "29", "hashOfConfig": "27"}, {"size": 4048, "mtime": 1749120613652, "results": "30", "hashOfConfig": "27"}, {"size": 3542, "mtime": 1749120590463, "results": "31", "hashOfConfig": "27"}, {"size": 960, "mtime": 1749120663980, "results": "32", "hashOfConfig": "27"}, {"size": 1291, "mtime": 1749120696983, "results": "33", "hashOfConfig": "27"}, {"size": 9859, "mtime": 1749124441199, "results": "34", "hashOfConfig": "27"}, {"size": 689, "mtime": 1749120961964, "results": "35", "hashOfConfig": "27"}, {"size": 7890, "mtime": 1749120795764, "results": "36", "hashOfConfig": "27"}, {"size": 9332, "mtime": 1749120841672, "results": "37", "hashOfConfig": "27"}, {"size": 12204, "mtime": 1749124457421, "results": "38", "hashOfConfig": "27"}, {"size": 14199, "mtime": 1749124610602, "results": "39", "hashOfConfig": "27"}, {"size": 1670, "mtime": 1749120992265, "results": "40", "hashOfConfig": "27"}, {"size": 718, "mtime": 1749120972363, "results": "41", "hashOfConfig": "27"}, {"size": 693, "mtime": 1749120982579, "results": "42", "hashOfConfig": "27"}, {"size": 6041, "mtime": 1749120745965, "results": "43", "hashOfConfig": "27"}, {"size": 3354, "mtime": 1749120515367, "results": "44", "hashOfConfig": "27"}, {"size": 4782, "mtime": 1749120718158, "results": "45", "hashOfConfig": "27"}, {"size": 3299, "mtime": 1749120492271, "results": "46", "hashOfConfig": "27"}, {"size": 2066, "mtime": 1749120683282, "results": "47", "hashOfConfig": "27"}, {"size": 1004, "mtime": 1749123081790, "results": "48", "hashOfConfig": "27"}, {"size": 4488, "mtime": 1749120572535, "results": "49", "hashOfConfig": "27"}, {"size": 2906, "mtime": 1749124388034, "results": "50", "hashOfConfig": "27"}, {"size": 9190, "mtime": 1749124661314, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j2gbhs", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Augment-projects\\UBI-CPV-T\\src\\index.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\reportWebVitals.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\App.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\theme\\theme.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\contexts\\AuthContext.tsx", ["127"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Layout.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadsPage.tsx", ["128", "129"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\documents\\DocumentsPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\auth\\LoginPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\dashboard\\DashboardPage.tsx", ["130"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadDetailsPage.tsx", ["131", "132", "133", "134"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\CreateLeadPage.tsx", ["135"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\common\\NotFoundPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\verification\\VerificationPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\profile\\ProfilePage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Sidebar.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\lead.service.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Header.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\auth.service.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\StatusChip.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\verification.service.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\api.service.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\CreateLeadPageSimple.tsx", ["136", "137", "138"], [], {"ruleId": "139", "severity": 1, "message": "140", "line": 28, "column": 6, "nodeType": "141", "endLine": 28, "endColumn": 8, "suggestions": "142"}, {"ruleId": "143", "severity": 1, "message": "144", "line": 32, "column": 37, "nodeType": "145", "messageId": "146", "endLine": 32, "endColumn": 47}, {"ruleId": "139", "severity": 1, "message": "147", "line": 50, "column": 6, "nodeType": "141", "endLine": 50, "endColumn": 39, "suggestions": "148"}, {"ruleId": "139", "severity": 1, "message": "149", "line": 69, "column": 6, "nodeType": "141", "endLine": 69, "endColumn": 12, "suggestions": "150"}, {"ruleId": "143", "severity": 1, "message": "151", "line": 11, "column": 3, "nodeType": "145", "messageId": "146", "endLine": 11, "endColumn": 7}, {"ruleId": "143", "severity": 1, "message": "152", "line": 12, "column": 3, "nodeType": "145", "messageId": "146", "endLine": 12, "endColumn": 10}, {"ruleId": "143", "severity": 1, "message": "153", "line": 15, "column": 3, "nodeType": "145", "messageId": "146", "endLine": 15, "endColumn": 10}, {"ruleId": "143", "severity": 1, "message": "154", "line": 63, "column": 9, "nodeType": "145", "messageId": "146", "endLine": 63, "endColumn": 19}, {"ruleId": "143", "severity": 1, "message": "155", "line": 26, "column": 22, "nodeType": "145", "messageId": "146", "endLine": 26, "endColumn": 35}, {"ruleId": "143", "severity": 1, "message": "156", "line": 12, "column": 3, "nodeType": "145", "messageId": "146", "endLine": 12, "endColumn": 7}, {"ruleId": "143", "severity": 1, "message": "157", "line": 13, "column": 3, "nodeType": "145", "messageId": "146", "endLine": 13, "endColumn": 14}, {"ruleId": "143", "severity": 1, "message": "158", "line": 14, "column": 3, "nodeType": "145", "messageId": "146", "endLine": 14, "endColumn": 13}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeAuth'. Either include it or remove the dependency array.", "ArrayExpression", ["159"], "@typescript-eslint/no-unused-vars", "'LeadStatus' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'loadLeads'. Either include it or remove the dependency array.", ["160"], "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["161"], "'Chip' is defined but never used.", "'Divider' is defined but never used.", "'Tooltip' is defined but never used.", "'isEditMode' is assigned a value but never used.", "'CreateAddress' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, {"desc": "166", "fix": "167"}, "Update the dependencies array to be: [initializeAuth]", {"range": "168", "text": "169"}, "Update the dependencies array to be: [loadLeads, page, rowsPerPage, statusFilter]", {"range": "170", "text": "171"}, "Update the dependencies array to be: [loadDashboardData, user]", {"range": "172", "text": "173"}, [878, 880], "[initializeAuth]", [1346, 1379], "[loadLeads, page, rowsPerPage, statusFilter]", [2133, 2139], "[loadDashboardData, user]"]