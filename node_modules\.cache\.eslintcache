[{"D:\\Augment-projects\\UBI-CPV-T\\src\\index.tsx": "1", "D:\\Augment-projects\\UBI-CPV-T\\src\\reportWebVitals.ts": "2", "D:\\Augment-projects\\UBI-CPV-T\\src\\App.tsx": "3", "D:\\Augment-projects\\UBI-CPV-T\\src\\theme\\theme.ts": "4", "D:\\Augment-projects\\UBI-CPV-T\\src\\contexts\\AuthContext.tsx": "5", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\ProtectedRoute.tsx": "6", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Layout.tsx": "7", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadsPage.tsx": "8", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\documents\\DocumentsPage.tsx": "9", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\auth\\LoginPage.tsx": "10", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\dashboard\\DashboardPage.tsx": "11", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadDetailsPage.tsx": "12", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\CreateLeadPage.tsx": "13", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\common\\NotFoundPage.tsx": "14", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\verification\\VerificationPage.tsx": "15", "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\profile\\ProfilePage.tsx": "16", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Sidebar.tsx": "17", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\lead.service.ts": "18", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Header.tsx": "19", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\auth.service.ts": "20", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\StatusChip.tsx": "21", "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\LoadingSpinner.tsx": "22", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\verification.service.ts": "23", "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\api.service.ts": "24"}, {"size": 554, "mtime": 1749120636486, "results": "25", "hashOfConfig": "26"}, {"size": 425, "mtime": 1749120656082, "results": "27", "hashOfConfig": "26"}, {"size": 2518, "mtime": 1749120627233, "results": "28", "hashOfConfig": "26"}, {"size": 4048, "mtime": 1749120613652, "results": "29", "hashOfConfig": "26"}, {"size": 3542, "mtime": 1749120590463, "results": "30", "hashOfConfig": "26"}, {"size": 960, "mtime": 1749120663980, "results": "31", "hashOfConfig": "26"}, {"size": 1291, "mtime": 1749120696983, "results": "32", "hashOfConfig": "26"}, {"size": 9887, "mtime": 1749120882169, "results": "33", "hashOfConfig": "26"}, {"size": 689, "mtime": 1749120961964, "results": "34", "hashOfConfig": "26"}, {"size": 7890, "mtime": 1749120795764, "results": "35", "hashOfConfig": "26"}, {"size": 9332, "mtime": 1749120841672, "results": "36", "hashOfConfig": "26"}, {"size": 12218, "mtime": 1749120919353, "results": "37", "hashOfConfig": "26"}, {"size": 12731, "mtime": 1749120951290, "results": "38", "hashOfConfig": "26"}, {"size": 1670, "mtime": 1749120992265, "results": "39", "hashOfConfig": "26"}, {"size": 718, "mtime": 1749120972363, "results": "40", "hashOfConfig": "26"}, {"size": 693, "mtime": 1749120982579, "results": "41", "hashOfConfig": "26"}, {"size": 6041, "mtime": 1749120745965, "results": "42", "hashOfConfig": "26"}, {"size": 3354, "mtime": 1749120515367, "results": "43", "hashOfConfig": "26"}, {"size": 4782, "mtime": 1749120718158, "results": "44", "hashOfConfig": "26"}, {"size": 3299, "mtime": 1749120492271, "results": "45", "hashOfConfig": "26"}, {"size": 2066, "mtime": 1749120683282, "results": "46", "hashOfConfig": "26"}, {"size": 1004, "mtime": 1749123081790, "results": "47", "hashOfConfig": "26"}, {"size": 4488, "mtime": 1749120572535, "results": "48", "hashOfConfig": "26"}, {"size": 2607, "mtime": 1749120473187, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j2gbhs", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Augment-projects\\UBI-CPV-T\\src\\index.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\reportWebVitals.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\App.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\theme\\theme.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\contexts\\AuthContext.tsx", ["122"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Layout.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadsPage.tsx", ["123", "124"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\documents\\DocumentsPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\auth\\LoginPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\dashboard\\DashboardPage.tsx", ["125"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\LeadDetailsPage.tsx", ["126", "127", "128", "129"], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\leads\\CreateLeadPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\common\\NotFoundPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\verification\\VerificationPage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\pages\\profile\\ProfilePage.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Sidebar.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\lead.service.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\layout\\Header.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\auth.service.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\StatusChip.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\verification.service.ts", [], [], "D:\\Augment-projects\\UBI-CPV-T\\src\\services\\api.service.ts", [], [], {"ruleId": "130", "severity": 1, "message": "131", "line": 28, "column": 6, "nodeType": "132", "endLine": 28, "endColumn": 8, "suggestions": "133"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 32, "column": 37, "nodeType": "136", "messageId": "137", "endLine": 32, "endColumn": 47}, {"ruleId": "130", "severity": 1, "message": "138", "line": 50, "column": 6, "nodeType": "132", "endLine": 50, "endColumn": 39, "suggestions": "139"}, {"ruleId": "130", "severity": 1, "message": "140", "line": 69, "column": 6, "nodeType": "132", "endLine": 69, "endColumn": 12, "suggestions": "141"}, {"ruleId": "134", "severity": 1, "message": "142", "line": 11, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 11, "endColumn": 7}, {"ruleId": "134", "severity": 1, "message": "143", "line": 12, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 12, "endColumn": 10}, {"ruleId": "134", "severity": 1, "message": "144", "line": 15, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 15, "endColumn": 10}, {"ruleId": "134", "severity": 1, "message": "145", "line": 63, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 63, "endColumn": 19}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeAuth'. Either include it or remove the dependency array.", "ArrayExpression", ["146"], "@typescript-eslint/no-unused-vars", "'LeadStatus' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'loadLeads'. Either include it or remove the dependency array.", ["147"], "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["148"], "'Chip' is defined but never used.", "'Divider' is defined but never used.", "'Tooltip' is defined but never used.", "'isEditMode' is assigned a value but never used.", {"desc": "149", "fix": "150"}, {"desc": "151", "fix": "152"}, {"desc": "153", "fix": "154"}, "Update the dependencies array to be: [initializeAuth]", {"range": "155", "text": "156"}, "Update the dependencies array to be: [loadLeads, page, rowsPerPage, statusFilter]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [loadDashboardData, user]", {"range": "159", "text": "160"}, [878, 880], "[initializeAuth]", [1346, 1379], "[loadLeads, page, rowsPerPage, statusFilter]", [2133, 2139], "[loadDashboardData, user]"]