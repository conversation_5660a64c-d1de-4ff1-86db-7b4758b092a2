using Microsoft.EntityFrameworkCore;
using UBI.CPV.API.Models.Entities;

namespace UBI.CPV.API.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<Lead> Leads { get; set; }
        public DbSet<LeadAddress> LeadAddresses { get; set; }
        public DbSet<LeadStatusHistory> LeadStatusHistory { get; set; }
        public DbSet<VerificationData> VerificationData { get; set; }
        public DbSet<DocumentType> DocumentTypes { get; set; }
        public DbSet<LeadDocument> LeadDocuments { get; set; }
        public DbSet<CroppedImage> CroppedImages { get; set; }
        public DbSet<VerificationDocument> VerificationDocuments { get; set; }
        public DbSet<AppSetting> AppSettings { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User entity configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Role).HasConversion<string>();

                // Self-referencing relationship
                entity.HasOne(e => e.Creator)
                      .WithMany(e => e.CreatedUsers)
                      .HasForeignKey(e => e.CreatedBy)
                      .OnDelete(DeleteBehavior.Restrict);

                // Relationships with Leads
                entity.HasMany(e => e.CreatedLeads)
                      .WithOne(e => e.Creator)
                      .HasForeignKey(e => e.CreatedBy)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(e => e.AssignedLeads)
                      .WithOne(e => e.AssignedAgent)
                      .HasForeignKey(e => e.AssignedTo)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(e => e.ReviewedLeads)
                      .WithOne(e => e.Reviewer)
                      .HasForeignKey(e => e.ReviewedBy)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Lead entity configuration
            modelBuilder.Entity<Lead>(entity =>
            {
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedDate);
                entity.HasIndex(e => e.CustomerName);
                entity.HasIndex(e => e.MobileNumber);

                entity.Property(e => e.Status).HasDefaultValue("new");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
            });

            // LeadAddress entity configuration
            modelBuilder.Entity<LeadAddress>(entity =>
            {
                entity.HasOne(e => e.Lead)
                      .WithMany(e => e.Addresses)
                      .HasForeignKey(e => e.LeadId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // LeadStatusHistory entity configuration
            modelBuilder.Entity<LeadStatusHistory>(entity =>
            {
                entity.HasIndex(e => e.LeadId);
                entity.HasIndex(e => e.Timestamp);

                entity.HasOne(e => e.Lead)
                      .WithMany(e => e.StatusHistory)
                      .HasForeignKey(e => e.LeadId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UpdatedByUser)
                      .WithMany(e => e.StatusHistories)
                      .HasForeignKey(e => e.UpdatedBy)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // VerificationData entity configuration
            modelBuilder.Entity<VerificationData>(entity =>
            {
                entity.HasIndex(e => e.LeadId).IsUnique();

                entity.HasOne(e => e.Lead)
                      .WithOne(e => e.VerificationData)
                      .HasForeignKey<VerificationData>(e => e.LeadId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // DocumentType entity configuration
            modelBuilder.Entity<DocumentType>(entity =>
            {
                entity.HasIndex(e => e.TypeName).IsUnique();
            });

            // LeadDocument entity configuration
            modelBuilder.Entity<LeadDocument>(entity =>
            {
                entity.HasIndex(e => e.LeadId);

                entity.HasOne(e => e.Lead)
                      .WithMany(e => e.Documents)
                      .HasForeignKey(e => e.LeadId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.DocumentType)
                      .WithMany(e => e.LeadDocuments)
                      .HasForeignKey(e => e.DocumentTypeId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.UploadedByUser)
                      .WithMany(e => e.UploadedDocuments)
                      .HasForeignKey(e => e.UploadedBy)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // CroppedImage entity configuration
            modelBuilder.Entity<CroppedImage>(entity =>
            {
                entity.HasIndex(e => e.LeadId);

                entity.HasOne(e => e.Lead)
                      .WithMany(e => e.CroppedImages)
                      .HasForeignKey(e => e.LeadId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CreatedByUser)
                      .WithMany(e => e.CroppedImages)
                      .HasForeignKey(e => e.CreatedBy)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // VerificationDocument entity configuration
            modelBuilder.Entity<VerificationDocument>(entity =>
            {
                entity.HasIndex(e => e.LeadId);

                entity.HasOne(e => e.Lead)
                      .WithMany(e => e.VerificationDocuments)
                      .HasForeignKey(e => e.LeadId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.DocumentType)
                      .WithMany(e => e.VerificationDocuments)
                      .HasForeignKey(e => e.DocumentTypeId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.UploadedByUser)
                      .WithMany(e => e.VerificationDocuments)
                      .HasForeignKey(e => e.UploadedBy)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // UserSession entity configuration
            modelBuilder.Entity<UserSession>(entity =>
            {
                entity.HasIndex(e => e.Token);
                entity.HasIndex(e => e.UserId);

                entity.HasOne(e => e.User)
                      .WithMany(e => e.Sessions)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // AppSetting entity configuration
            modelBuilder.Entity<AppSetting>(entity =>
            {
                entity.HasIndex(e => e.SettingKey).IsUnique();

                entity.HasOne(e => e.UpdatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.UpdatedBy)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // AuditLog entity configuration
            modelBuilder.Entity<AuditLog>(entity =>
            {
                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed DocumentTypes
            modelBuilder.Entity<DocumentType>().HasData(
                new DocumentType { DocumentTypeId = 1, TypeName = "PDF_DOCUMENT", Description = "Main PDF document uploaded during lead creation" },
                new DocumentType { DocumentTypeId = 2, TypeName = "KYC_DOCUMENT", Description = "KYC documents like Aadhaar, PAN, etc." },
                new DocumentType { DocumentTypeId = 3, TypeName = "PHOTO", Description = "Applicant and premises photos" },
                new DocumentType { DocumentTypeId = 4, TypeName = "ADDRESS_PROOF", Description = "Address proof documents" },
                new DocumentType { DocumentTypeId = 5, TypeName = "INCOME_PROOF", Description = "Salary slips, bank statements, etc." },
                new DocumentType { DocumentTypeId = 6, TypeName = "ADDITIONAL", Description = "Any additional supporting documents" }
            );

            // Seed AppSettings
            modelBuilder.Entity<AppSetting>().HasData(
                new AppSetting { SettingId = 1, SettingKey = "MAX_FILE_SIZE", SettingValue = "********", Description = "Maximum file upload size in bytes (25MB)" },
                new AppSetting { SettingId = 2, SettingKey = "ALLOWED_FILE_TYPES", SettingValue = "pdf,jpg,jpeg,png,gif", Description = "Allowed file extensions for upload" },
                new AppSetting { SettingId = 3, SettingKey = "SESSION_TIMEOUT", SettingValue = "480", Description = "Session timeout in minutes" },
                new AppSetting { SettingId = 4, SettingKey = "PASSWORD_MIN_LENGTH", SettingValue = "8", Description = "Minimum password length" },
                new AppSetting { SettingId = 5, SettingKey = "ENABLE_EMAIL_NOTIFICATIONS", SettingValue = "true", Description = "Enable email notifications" }
            );
        }
    }
}
