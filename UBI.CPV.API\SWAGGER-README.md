# UBI-CPV API Swagger Documentation

## Overview

Swagger (OpenAPI) documentation has been successfully integrated into the UBI-CPV API project. This provides interactive API documentation that allows developers to explore, test, and understand the API endpoints.

## Accessing Swagger UI

### Development Environment
- **URL**: `https://localhost:59358/swagger` or `http://localhost:59359/swagger`
- **Status**: ✅ Enabled in all environments

### Production Environment
- **URL**: `https://your-production-domain/swagger`
- **Status**: ✅ Enabled (can be restricted if needed)

## Features Implemented

### 1. Enhanced Swagger Configuration
- ✅ Detailed API information (title, version, description)
- ✅ Contact and license information
- ✅ XML documentation comments integration
- ✅ JWT Bearer authentication support
- ✅ Custom styling with UBI branding

### 2. Authentication Integration
- ✅ JWT Bearer token authentication in Swagger UI
- ✅ "Authorize" button for easy token input
- ✅ Automatic token inclusion in API requests

### 3. API Documentation
- ✅ XML documentation comments for controllers and actions
- ✅ Swagger annotations for detailed operation descriptions
- ✅ Response status codes and types
- ✅ Parameter descriptions and examples

### 4. UI Enhancements
- ✅ Custom CSS styling with UBI colors
- ✅ Enhanced readability and user experience
- ✅ Request/response examples
- ✅ Operation grouping by controller

## How to Use Swagger UI

### 1. Authentication
1. Click the "Authorize" button (🔒) at the top right
2. Enter your JWT token in the format: `Bearer your-jwt-token-here`
3. Click "Authorize" to apply the token to all requests

### 2. Testing API Endpoints
1. Expand any endpoint section (e.g., Auth, Leads, Documents)
2. Click "Try it out" button
3. Fill in required parameters
4. Click "Execute" to send the request
5. View the response below

### 3. Getting a JWT Token
1. Use the `/api/Auth/login` endpoint first
2. Provide valid credentials:
   ```json
   {
     "username": "admin",
     "password": "password",
     "role": "Admin"
   }
   ```
3. Copy the token from the response
4. Use it in the "Authorize" section

## Available Endpoints

### Authentication (`/api/Auth`)
- `POST /api/Auth/login` - User login
- `POST /api/Auth/logout` - User logout
- `POST /api/Auth/refresh-token` - Refresh JWT token
- `GET /api/Auth/me` - Get current user info

### Leads Management (`/api/Leads`)
- `GET /api/Leads` - Get leads with pagination
- `POST /api/Leads` - Create new lead
- `GET /api/Leads/{id}` - Get lead by ID
- `PUT /api/Leads/{id}` - Update lead
- `DELETE /api/Leads/{id}` - Delete lead

### Documents (`/api/Documents`)
- `POST /api/Documents/leads/{leadId}/upload` - Upload lead document
- `POST /api/Documents/leads/{leadId}/verification-documents` - Upload verification document
- `GET /api/Documents/leads/{leadId}` - Get lead documents
- `GET /api/Documents/types` - Get document types

### Verification (`/api/Verification`)
- Various verification endpoints for different user roles

## Default Test Credentials

```json
{
  "Admin": {
    "username": "admin",
    "password": "password",
    "role": "Admin"
  },
  "Agent": {
    "username": "agent",
    "password": "password",
    "role": "Agent"
  },
  "Supervisor": {
    "username": "supervisor",
    "password": "password",
    "role": "Supervisor"
  }
}
```

## Technical Details

### Packages Added
- `Swashbuckle.AspNetCore` (6.5.0) - Core Swagger functionality
- `Swashbuckle.AspNetCore.Annotations` (6.5.0) - Enhanced annotations

### Configuration Files Modified
- `UBI.CPV.API.csproj` - Added XML documentation generation
- `Program.cs` - Enhanced Swagger configuration
- `Controllers/*.cs` - Added XML documentation comments

### Custom Files Added
- `wwwroot/swagger-ui/custom.css` - Custom styling
- `SWAGGER-README.md` - This documentation file

## Security Considerations

- JWT tokens are required for most endpoints
- Swagger UI is currently enabled in all environments
- Consider restricting Swagger access in production if needed
- All API endpoints respect the existing authorization policies

## Troubleshooting

### Common Issues
1. **401 Unauthorized**: Ensure you've provided a valid JWT token
2. **Token expired**: Use the refresh token endpoint or login again
3. **CORS issues**: The API is configured to allow frontend origins

### Support
For technical support or questions about the API documentation, contact the development team.
