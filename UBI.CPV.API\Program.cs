using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Reflection;
using System.Text;
using UBI.CPV.API.Data;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/ubi-cpv-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllers();

// Configure Entity Framework with SQL Server
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure JWT Authentication
var jwtKey = builder.Configuration["Jwt:Key"] ?? "your-super-secret-jwt-key-that-is-at-least-32-characters-long";
var key = Encoding.ASCII.GetBytes(jwtKey);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"] ?? "UBI-CPV-API",
        ValidateAudience = true,
        ValidAudience = builder.Configuration["Jwt:Audience"] ?? "UBI-CPV-Client",
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>()
            ?? new[] { "http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:9999", "http://127.0.0.1:9999" };

        policy.WithOrigins(allowedOrigins)
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "UBI-CPV API",
        Version = "v1.0.0",
        Description = "Union Bank of India - Customer Premises Verification API\n\n" +
                     "This API provides endpoints for managing customer verification processes, " +
                     "including lead management, document uploads, and verification workflows.",
        Contact = new OpenApiContact
        {
            Name = "UBI-CPV Support",
            Email = "<EMAIL>"
        },
        License = new OpenApiLicense
        {
            Name = "UBI Internal Use",
            Url = new Uri("https://www.unionbankofindia.co.in")
        }
    });

    // Include XML comments for better documentation
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme.\n\n" +
                     "Enter 'Bearer' [space] and then your token in the text input below.\n\n" +
                     "Example: 'Bearer 12345abcdef'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header
            },
            new List<string>()
        }
    });

    // Enable annotations for better documentation
    c.EnableAnnotations();

    // Group actions by controller
    c.TagActionsBy(api => new[] { api.GroupName ?? api.ActionDescriptor.RouteValues["controller"] });
    c.DocInclusionPredicate((name, api) => true);
});

// Configure file upload size limits
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 26214400; // 25MB
});

builder.WebHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestBodySize = 26214400; // 25MB
});

var app = builder.Build();

// Configure the HTTP request pipeline.
// Enable Swagger in all environments (can be restricted later if needed)
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "UBI-CPV API v1.0.0");
    c.RoutePrefix = "swagger";
    c.DocumentTitle = "UBI-CPV API Documentation";
    c.DefaultModelsExpandDepth(-1); // Hide models section by default
    c.DefaultModelRendering(Swashbuckle.AspNetCore.SwaggerUI.ModelRendering.Model);
    c.DisplayRequestDuration();
    c.EnableDeepLinking();
    c.EnableFilter();
    c.ShowExtensions();
    c.EnableValidator();
    c.SupportedSubmitMethods(Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Get,
                           Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Post,
                           Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Put,
                           Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Delete);

    // Add custom CSS for better styling
    c.InjectStylesheet("/swagger-ui/custom.css");
});

// Enable CORS
app.UseCors("AllowFrontend");

// Serve static files (for uploaded documents)
app.UseStaticFiles();

// Enable authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

// Add health check endpoint
app.MapGet("/health", () => Results.Ok(new {
    status = "healthy",
    timestamp = DateTime.UtcNow,
    version = "1.0.0"
}));

// Map controllers
app.MapControllers();

// Ensure database is migrated and seeded
try
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        // Apply any pending migrations
        context.Database.Migrate();

        // Seed default users if they don't exist
        if (!context.Users.Any())
        {
            var defaultUsers = new[]
            {
                new UBI.CPV.API.Models.Entities.User
                {
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"),
                    Salt = "admin_salt",
                    Role = "Admin",
                    FirstName = "System",
                    LastName = "Administrator",
                    PhoneNumber = "**********",
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                },
                new UBI.CPV.API.Models.Entities.User
                {
                    Username = "agent",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"),
                    Salt = "agent_salt",
                    Role = "Agent",
                    FirstName = "Test",
                    LastName = "Agent",
                    PhoneNumber = "9999999998",
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                },
                new UBI.CPV.API.Models.Entities.User
                {
                    Username = "supervisor",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"),
                    Salt = "supervisor_salt",
                    Role = "Supervisor",
                    FirstName = "Test",
                    LastName = "Supervisor",
                    PhoneNumber = "9999999997",
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                }
            };

            context.Users.AddRange(defaultUsers);
            context.SaveChanges();

            Log.Information("Default users created successfully");
        }
    }
}
catch (Exception ex)
{
    Log.Error(ex, "An error occurred while creating the database or seeding data");
}

Log.Information("UBI-CPV API starting up...");

app.Run();

Log.Information("UBI-CPV API shut down complete");
Log.CloseAndFlush();
