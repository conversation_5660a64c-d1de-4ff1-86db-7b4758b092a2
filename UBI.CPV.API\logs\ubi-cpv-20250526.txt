[2025-05-26 17:24:46.549 +05:30 INF] Default users created successfully {}
[2025-05-26 17:24:46.630 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-26 17:24:46.648 +05:30 WRN] The WebRootPath was not found: /2025-05-26 17:24:46.648 +05:30 [WRN] The WebRootPath was not found: /Users/<USER>/Documents/augment-projects/Expired UBI-CPV/UBI.CPV.API/wwwroot. Static files may be unavailable.
crosoft.AspNetCore.StaticFiles.StaticFileMiddleware"}
[2025-05-26 17:25:41.315 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-26 17:25:41.413 +05:30 WRN] The WebRootPath was not fou2025-05-26 17:25:41.413 +05:30 [WRN] The WebRootPath was not found: /Users/<USER>/Documents/augment-projects/Expired UBI-CPV/UBI.CPV.API/wwwroot. Static files may be unavailable.
2025-05-26 17:26:47.653 +05:30 [INF] UBI-CPV API shut down complete
26 17:26:47.653 +05:30 INF] UBI-CPV API shut down complete {}
[2025-05-26 17:27:12.454 +05:30 INF] UBI-CPV API starting up... {}
2025-05-26 17:27:12.454 +05:30 [INF] UBI-CPV API starting up...
[2025-05-26 17:28:01.931 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-26 17:29:03.404 +05:30 INF] UBI-CPV API shut down compl2025-05-26 17:29:03.404 +05:30 [INF] UBI-CPV API shut down complete
[2025-05-26 17:29:42.897 +05:30 INF] UBI-CPV API starting up... {}
2025-05-26 17:29:42.897 +05:30 [INF] UBI-CPV API starting up...
[2025-05-26 17:31:57.379 +05:30 INF] UBI-CPV API starting up... {}
2025-05-26 17:31:57.379 +05:30 [INF] UBI-CPV API starting up...
[2025-05-26 17:32:19.516 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-26 17:32:19.646 +05:30 ERR] Hosting failed to start {"E2025-05-26 17:32:19.646 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
tOnFirstException, List`1 exceptions, Func`3 operation)
[2025-05-26 17:32:58.335 +05:30 INF] UBI-CPV API starting up... {}
2025-05-26 17:32:58.335 +05:30 [INF] UBI-CPV API starting up...
[2025-05-26 17:33:22.566 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-26 17:33:22.685 +05:30 ERR] Hosting failed to start {"E2025-05-26 17:33:22.685 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5001: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
tOnFirstException, List`1 exceptions, Func`3 operation)
[2025-05-26 17:33:37.281 +05:30 INF] UBI-CPV API starting up... {}
2025-05-26 17:33:37.281 +05:30 [INF] UBI-CPV API starting up...
[2025-05-26 17:34:09.342 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-26 17:34:09.431 +05:30 INF] User profile is available. 2025-05-26 17:34:09.431 +05:30 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-05-26 17:34:09.457 +05:30 [INF] Now listening on: http://localhost:5002
2025-05-26 17:34:09.458 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 17:34:09.458 +05:30 [INF] Hosting environment: Development
2025-05-26 17:34:09.458 +05:30 [INF] Content root path: /Users/<USER>/Documents/augment-projects/Expired UBI-CPV/UBI.CPV.API
Hosting.Lifetime"}
[2025-05-26 17:34:09.458 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-26 17:34:09.458 +05:30 INF] Content root path: /Users/<USER>/Documents/augment-projects/Expired UBI-CPV/UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-26 17:35:11.194 +05:30 ERR] An error occurred while creating the database or seeding data {}
System.ArgumentException: Connection string keyword 'server' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in /Users/<USER>/Documents/augment-projects/Expired UBI-CPV/UBI.CPV.API/Program.cs:line 146
[2025-05-26 17:35:11.272 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-26 17:35:11.278 +05:30 INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest. {"EventId":{"Id":62,"Name":"UsingProfileAsKeyRepository"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-26 17:35:11.298 +05:30 INF] Now listening on: http://localhost:5002 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-26 17:35:11.298 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-26 17:35:11.298 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-26 17:35:11.298 +05:30 INF] Content root path: /Users/<USER>/Documents/augment-projects/Expired UBI-CPV/UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
PV.API/Program.cs:line 146
2025-05-26 17:35:11.272 +05:30 [INF] UBI-CPV API starting up...
2025-05-26 17:35:11.278 +05:30 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-05-26 17:35:11.298 +05:30 [INF] Now listening on: http://localhost:5002
2025-05-26 17:35:11.298 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 17:35:11.298 +05:30 [INF] Hosting environment: Development
2025-05-26 17:35:11.298 +05:30 [INF] Content root path: /Users/<USER>/Documents/augment-projects/Expired UBI-CPV/UBI.CPV.API
