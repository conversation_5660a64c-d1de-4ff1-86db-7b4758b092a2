﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\swagger-ui\custom.css'))">
      <SourceType>Package</SourceType>
      <SourceId>UBI.CPV.API</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/UBI.CPV.API</BasePath>
      <RelativePath>swagger-ui/custom.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2da00dyc2e</Fingerprint>
      <Integrity>dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\swagger-ui\custom.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2661f2ea-e93b-4cdc-8d4a-fb5724de73b3.pdf'))">
      <SourceType>Package</SourceType>
      <SourceId>UBI.CPV.API</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/UBI.CPV.API</BasePath>
      <RelativePath>uploads/2661f2ea-e93b-4cdc-8d4a-fb5724de73b3.pdf</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>01hrg5tjes</Fingerprint>
      <Integrity>DJ0rh89u/ungLz4hXKnlFGAckybixorCWL9gSPJg+Rw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2661f2ea-e93b-4cdc-8d4a-fb5724de73b3.pdf'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf'))">
      <SourceType>Package</SourceType>
      <SourceId>UBI.CPV.API</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/UBI.CPV.API</BasePath>
      <RelativePath>uploads/9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vtzgwhd4pm</Fingerprint>
      <Integrity>Y+2SrX/HGeJWzofDHMwmBKndEXhIAZQasXCsGJtfdRA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\9e4d768d-bbe4-4560-9a1b-9f7d6372553b.pdf'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>