2025-05-29 20:06:35.271 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 20:06:35.478 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 20:06:35.550 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 20:06:35.575 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 20:06:35.617 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 20:06:35.691 +05:30 [INF] Applying migration '20250529130711_InitialCreate'.
2025-05-29 20:06:35.900 +05:30 [ERR] Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
);
2025-05-29 20:06:36.098 +05:30 [ERR] An error occurred while creating the database or seeding data
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:dd5d12ed-b5c2-4ab6-aff6-013e604a180b
Error Number:2714,State:6,Class:16
2025-05-29 20:06:36.133 +05:30 [INF] UBI-CPV API starting up...
2025-05-29 20:06:36.210 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 20:06:36.736 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-29 20:06:36.749 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-29 20:06:37.016 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 20:06:37.027 +05:30 [INF] Hosting environment: Development
2025-05-29 20:06:37.035 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-05-29 20:06:37.111 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null
2025-05-29 20:06:37.584 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 485.3584ms
2025-05-29 20:06:38.019 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null
2025-05-29 20:06:38.065 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null
2025-05-29 20:06:38.240 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null
2025-05-29 20:06:38.291 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/_framework/aspnetcore-browser-refresh.js - null null
2025-05-29 20:06:38.290 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null
2025-05-29 20:06:38.427 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/_framework/aspnetcore-browser-refresh.js - 200 13774 application/javascript; charset=utf-8 136.0517ms
2025-05-29 20:06:38.290 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null
2025-05-29 20:06:38.304 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-29 20:06:38.350 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 331.0894ms
2025-05-29 20:06:38.490 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/_vs/browserLink - null null
2025-05-29 20:06:38.566 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404
2025-05-29 20:06:38.574 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-29 20:06:38.433 +05:30 [INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css'
2025-05-29 20:06:38.470 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-29 20:06:38.670 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 380.8186ms
2025-05-29 20:06:38.683 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/_vs/browserLink - 200 null text/javascript; charset=UTF-8 192.5114ms
2025-05-29 20:06:38.673 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 433.6909ms
2025-05-29 20:06:38.701 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 411.0126ms
2025-05-29 20:06:38.520 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 454.2058ms
2025-05-29 20:06:39.264 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null
2025-05-29 20:06:39.340 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null
2025-05-29 20:06:39.368 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-29 20:06:39.370 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 105.5595ms
2025-05-29 20:06:39.407 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 66.0361ms
2025-05-29 20:06:52.983 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null
2025-05-29 20:06:53.033 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:53.070 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 87.1062ms
2025-05-29 20:06:53.134 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 55
2025-05-29 20:06:53.191 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:53.248 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:06:53.316 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-29 20:06:57.010 +05:30 [INF] Executed DbCommand (85ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:06:57.974 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7;
2025-05-29 20:06:58.097 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'.
2025-05-29 20:06:58.162 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 4780.7116ms
2025-05-29 20:06:58.183 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:06:58.212 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 200 null application/json; charset=utf-8 5078.1374ms
2025-05-29 20:06:58.531 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:06:58.596 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:06:58.586 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:06:58.606 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:06:58.697 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:58.691 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:58.714 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:58.706 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:58.761 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 164.9209ms
2025-05-29 20:06:58.845 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 240.6798ms
2025-05-29 20:06:58.809 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 278.2409ms
2025-05-29 20:06:58.873 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:06:58.865 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 279.4952ms
2025-05-29 20:06:58.898 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:06:58.965 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:58.979 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:59.151 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:06:59.151 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:06:59.205 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:06:59.208 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:06:59.604 +05:30 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-05-29 20:06:59.614 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:06:59.674 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:06:59.700 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:06:59.720 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 464.4637ms
2025-05-29 20:06:59.766 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:06:59.803 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 904.6188ms
2025-05-29 20:06:59.818 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:06:59.866 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:06:59.884 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:06:59.902 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:06:59.916 +05:30 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:06:59.979 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:06:59.997 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:07:00.055 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:07:00.059 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 795.7559ms
2025-05-29 20:07:00.096 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 145.4338ms
2025-05-29 20:07:00.099 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:00.130 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:00.133 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 1259.3285ms
2025-05-29 20:07:00.143 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:00.187 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 369.2599ms
2025-05-29 20:07:00.202 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:00.243 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:00.256 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:00.284 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:07:00.321 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:07:00.353 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:07:00.386 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 107.3777ms
2025-05-29 20:07:00.404 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:00.420 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 277.0222ms
2025-05-29 20:07:02.378 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:02.378 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:02.383 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:02.385 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:02.448 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:02.453 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:02.458 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:02.463 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:02.507 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 128.6191ms
2025-05-29 20:07:02.509 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 131.1755ms
2025-05-29 20:07:02.511 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 128.1497ms
2025-05-29 20:07:02.513 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 127.7018ms
2025-05-29 20:07:02.591 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:02.584 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:02.647 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:02.654 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:02.688 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:02.690 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:02.721 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:02.724 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:02.782 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:07:02.823 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:07:02.860 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:07:02.860 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:07:02.909 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 133.322ms
2025-05-29 20:07:02.909 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:07:02.942 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:02.945 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 174.5218ms
2025-05-29 20:07:02.973 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 389.1784ms
2025-05-29 20:07:02.977 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:02.982 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:03.050 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 458.6022ms
2025-05-29 20:07:03.059 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:03.065 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:03.129 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:03.133 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:03.160 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:03.162 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:03.204 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:03.210 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:07:03.264 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:07:03.268 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:07:03.319 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 117.7498ms
2025-05-29 20:07:03.321 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:07:03.364 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:03.371 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:07:03.416 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 433.8166ms
2025-05-29 20:07:03.423 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 164.507ms
2025-05-29 20:07:03.476 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:03.497 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 432.5265ms
2025-05-29 20:07:18.342 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null
2025-05-29 20:07:18.342 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null
2025-05-29 20:07:18.421 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:18.432 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:18.475 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 133.3407ms
2025-05-29 20:07:18.479 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 137.228ms
2025-05-29 20:07:18.571 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null
2025-05-29 20:07:18.617 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:18.650 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:07:18.686 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:19.004 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-29 20:07:19.191 +05:30 [INF] Executed DbCommand (104ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:07:22.452 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:07:22.529 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 3811.4846ms
2025-05-29 20:07:22.554 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:07:22.575 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 4003.863ms
2025-05-29 20:07:22.585 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null
2025-05-29 20:07:22.656 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:22.672 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:07:22.686 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:22.713 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:07:24.520 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:07:24.570 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 1862.7474ms
2025-05-29 20:07:24.613 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:07:24.651 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 2066.5836ms
2025-05-29 20:07:34.701 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:34.702 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:34.702 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:34.702 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:34.738 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:34.929 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:34.984 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 282.4717ms
2025-05-29 20:07:34.967 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 266.6007ms
2025-05-29 20:07:34.947 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:34.964 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:35.100 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:35.121 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:35.127 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 424.6639ms
2025-05-29 20:07:35.130 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 428.4975ms
2025-05-29 20:07:35.282 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:35.270 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:35.387 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:35.390 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:35.449 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:35.454 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:35.583 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:07:35.597 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:07:35.648 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:07:35.653 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:07:35.700 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:07:35.705 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 116.7486ms
2025-05-29 20:07:35.731 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 156.513ms
2025-05-29 20:07:35.775 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:35.779 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:35.849 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 749.0974ms
2025-05-29 20:07:35.856 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 735.4135ms
2025-05-29 20:07:35.865 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:07:36.023 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:07:36.040 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:36.099 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:07:36.103 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:36.134 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:36.173 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:36.255 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:07:36.269 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:07:36.361 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:07:36.371 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:07:36.470 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:07:36.473 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 210.508ms
2025-05-29 20:07:36.577 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:07:36.584 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:07:36.621 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 271.0973ms
2025-05-29 20:07:36.679 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:07:36.672 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 806.8026ms
2025-05-29 20:07:36.758 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 735.3935ms
2025-05-29 20:08:03.691 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:03.691 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:08:03.691 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:03.694 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:08:03.830 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:03.837 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:03.844 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:03.850 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:03.922 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 231.5278ms
2025-05-29 20:08:03.911 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 220.106ms
2025-05-29 20:08:03.942 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 250.8716ms
2025-05-29 20:08:04.010 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 316.5316ms
2025-05-29 20:08:04.038 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:08:04.046 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:04.139 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:04.147 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:04.184 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 146.3507ms
2025-05-29 20:08:04.188 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:04.191 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:08:04.270 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:08:04.275 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:04.288 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:04.401 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:08:04.402 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 210.6832ms
2025-05-29 20:08:04.529 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:08:04.547 +05:30 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:08:04.600 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:08:04.633 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 248.8575ms
2025-05-29 20:08:04.653 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:04.673 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 626.8266ms
2025-05-29 20:08:04.685 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:04.773 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:04.816 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:04.867 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:04.933 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:08:04.989 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:08:05.031 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:08:05.064 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 139.2338ms
2025-05-29 20:08:05.083 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:05.107 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 421.9043ms
2025-05-29 20:08:10.121 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null
2025-05-29 20:08:10.121 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null
2025-05-29 20:08:10.216 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:10.231 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:10.277 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 155.8007ms
2025-05-29 20:08:10.279 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 158.2412ms
2025-05-29 20:08:10.335 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null
2025-05-29 20:08:10.392 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:10.424 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:08:10.442 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:10.550 +05:30 [INF] Executed DbCommand (78ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:08:10.646 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:08:10.724 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 255.168ms
2025-05-29 20:08:10.774 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:08:10.845 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 510.1309ms
2025-05-29 20:08:10.856 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null
2025-05-29 20:08:10.925 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:10.941 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:08:10.958 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:11.002 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:08:11.162 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:08:11.258 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 269.3334ms
2025-05-29 20:08:11.304 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:08:11.369 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 512.4716ms
2025-05-29 20:08:12.658 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:08:12.658 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:08:12.658 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:08:12.658 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:08:12.771 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:12.763 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:12.790 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:12.780 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:12.848 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 189.9945ms
2025-05-29 20:08:12.828 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 170.324ms
2025-05-29 20:08:12.915 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 257.2181ms
2025-05-29 20:08:12.899 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 241.3926ms
2025-05-29 20:08:12.996 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:08:12.988 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:08:13.055 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:13.061 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:13.101 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:08:13.105 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:13.140 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:13.142 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:13.291 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:08:13.291 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:08:13.424 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:08:13.433 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:08:13.477 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 203.591ms
2025-05-29 20:08:13.483 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:08:13.527 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:08:13.532 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 255.9117ms
2025-05-29 20:08:13.586 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 590.9037ms
2025-05-29 20:08:13.593 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:13.596 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:08:13.665 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 676.929ms
2025-05-29 20:08:13.673 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:13.676 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:08:13.736 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:08:13.743 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:13.772 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:13.775 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:13.841 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:08:13.841 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:13.906 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:08:13.916 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:08:13.985 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 151.1312ms
2025-05-29 20:08:14.027 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:08:14.156 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:08:14.174 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:08:14.279 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 368.0588ms
2025-05-29 20:08:14.274 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 677.3774ms
2025-05-29 20:08:14.431 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:14.515 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 839.8518ms
2025-05-29 20:08:17.274 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:17.277 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:17.277 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:08:17.277 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:08:17.440 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:17.458 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:17.478 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:17.497 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:17.598 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 324.6015ms
2025-05-29 20:08:17.601 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 324.0107ms
2025-05-29 20:08:17.604 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 326.8625ms
2025-05-29 20:08:17.609 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 331.2915ms
2025-05-29 20:08:17.749 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:17.779 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:08:17.866 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:17.876 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:17.942 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:17.945 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 166.0782ms
2025-05-29 20:08:17.952 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:08:18.040 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:18.055 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:08:18.066 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:18.153 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 201.4847ms
2025-05-29 20:08:18.175 +05:30 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:08:18.183 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:08:18.262 +05:30 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:08:18.319 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:08:18.363 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 224.1353ms
2025-05-29 20:08:18.393 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:18.423 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 673.9323ms
2025-05-29 20:08:18.437 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:08:18.547 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:08:18.600 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:18.641 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:08:18.714 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:08:18.810 +05:30 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:08:18.858 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:08:18.890 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 188.6927ms
2025-05-29 20:08:18.927 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:08:18.946 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 509.7017ms
2025-05-29 20:09:02.044 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:02.048 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:02.065 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:02.084 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:02.194 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:02.200 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:02.229 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:02.294 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 249.9346ms
2025-05-29 20:09:02.280 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:02.309 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 263.5406ms
2025-05-29 20:09:02.329 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 263.5975ms
2025-05-29 20:09:02.365 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:02.399 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 315.5909ms
2025-05-29 20:09:02.445 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:02.453 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:02.518 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:02.521 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:02.557 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:02.559 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:02.594 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:02.606 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:09:02.665 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:02.695 +05:30 [INF] Executed DbCommand (23ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:09:02.696 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:02.749 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:09:02.751 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 94.1135ms
2025-05-29 20:09:02.802 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 204.1911ms
2025-05-29 20:09:02.806 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:02.847 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:02.849 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 404.8228ms
2025-05-29 20:09:02.859 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:02.918 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 552.5145ms
2025-05-29 20:09:02.931 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:02.936 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:03.013 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:03.017 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:03.051 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:03.055 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:03.107 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:03.115 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:03.159 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:09:03.162 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:03.206 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:09:03.209 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 98.0967ms
2025-05-29 20:09:03.268 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:09:03.272 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:03.327 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 172.6843ms
2025-05-29 20:09:03.330 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 470.4021ms
2025-05-29 20:09:03.365 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:03.389 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 457.6928ms
2025-05-29 20:09:03.529 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:09:03.529 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:03.529 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:09:03.610 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:03.616 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:03.626 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:03.684 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 155.0445ms
2025-05-29 20:09:03.688 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:03.691 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 161.7049ms
2025-05-29 20:09:03.800 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:09:03.804 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:03.886 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:03.908 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:03.992 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 192.902ms
2025-05-29 20:09:03.999 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:09:04.004 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:04.088 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:09:04.097 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:04.100 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 202.6838ms
2025-05-29 20:09:04.157 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 158.5772ms
2025-05-29 20:09:04.163 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:04.217 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:09:04.220 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 691.0344ms
2025-05-29 20:09:04.260 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:04.292 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:04.311 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:04.340 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:04.408 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:04.449 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:04.473 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 76.0069ms
2025-05-29 20:09:04.493 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:04.512 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 251.6525ms
2025-05-29 20:09:43.044 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:43.056 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:09:43.076 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:43.082 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:09:43.183 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.192 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.199 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.208 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.280 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 236.4587ms
2025-05-29 20:09:43.293 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 237.4525ms
2025-05-29 20:09:43.398 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 322.0272ms
2025-05-29 20:09:43.401 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 319.739ms
2025-05-29 20:09:43.433 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:09:43.419 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:43.564 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.574 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.608 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 175.1802ms
2025-05-29 20:09:43.611 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:43.614 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:09:43.672 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:09:43.675 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:43.680 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.755 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 140.2326ms
2025-05-29 20:09:43.756 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:43.804 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:09:43.810 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:43.851 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 106.3778ms
2025-05-29 20:09:43.877 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:43.893 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 474.1165ms
2025-05-29 20:09:43.903 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:43.945 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:43.968 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:43.996 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:44.050 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:44.102 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:44.125 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 81.5737ms
2025-05-29 20:09:44.142 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:44.158 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 254.7618ms
2025-05-29 20:09:51.288 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:51.289 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:51.289 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:51.289 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:51.396 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:51.401 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:51.412 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:51.418 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:51.506 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 218.0941ms
2025-05-29 20:09:51.509 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 219.6788ms
2025-05-29 20:09:51.511 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 221.5751ms
2025-05-29 20:09:51.513 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 223.5007ms
2025-05-29 20:09:51.594 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:51.583 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:51.658 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:51.668 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:51.701 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:51.703 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:51.732 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:51.734 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:51.798 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:51.812 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:09:51.885 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:51.924 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:09:51.981 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 187.2048ms
2025-05-29 20:09:51.992 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:09:52.043 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:52.046 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 241.4526ms
2025-05-29 20:09:52.078 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 484.9341ms
2025-05-29 20:09:52.082 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:52.088 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:52.149 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 565.9429ms
2025-05-29 20:09:52.156 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:52.159 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:52.210 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:52.215 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:52.241 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:52.244 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:52.285 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:52.286 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:52.328 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:52.335 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:09:52.366 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 87.6267ms
2025-05-29 20:09:52.375 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:09:52.408 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:52.412 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:09:52.456 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 124.6109ms
2025-05-29 20:09:52.451 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 363.6094ms
2025-05-29 20:09:52.508 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:52.535 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 376.04ms
2025-05-29 20:09:58.008 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:58.011 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:58.011 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:58.011 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:58.175 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:58.190 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:58.183 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:58.197 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:58.287 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 278.4462ms
2025-05-29 20:09:58.290 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 279.2065ms
2025-05-29 20:09:58.293 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 282.8423ms
2025-05-29 20:09:58.296 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 285.4499ms
2025-05-29 20:09:58.387 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:58.424 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:58.454 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:58.461 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:58.502 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:58.505 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:58.545 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:58.548 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:58.601 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:09:58.611 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:58.679 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:58.709 +05:30 [INF] Executed DbCommand (37ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:09:58.748 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 144.1843ms
2025-05-29 20:09:58.757 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:09:58.808 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:58.814 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 220.7633ms
2025-05-29 20:09:58.867 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 443.3549ms
2025-05-29 20:09:58.873 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:58.880 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:09:59.003 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 616.1268ms
2025-05-29 20:09:59.016 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:59.016 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:09:59.137 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:59.151 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:09:59.214 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:59.217 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:59.284 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:09:59.285 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:09:59.370 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:09:59.381 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:09:59.445 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 171.4544ms
2025-05-29 20:09:59.467 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:09:59.503 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:09:59.512 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:09:59.553 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 673.6817ms
2025-05-29 20:09:59.556 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 192.1108ms
2025-05-29 20:09:59.597 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:09:59.612 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 596.4492ms
2025-05-29 20:10:01.332 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:10:01.333 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:10:01.333 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:10:01.448 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:01.458 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:01.469 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:01.535 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:01.539 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 206.0367ms
2025-05-29 20:10:01.543 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 209.4949ms
2025-05-29 20:10:01.602 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:10:01.610 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:10:01.668 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:10:01.670 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:01.728 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:10:01.730 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 120.2814ms
2025-05-29 20:10:01.736 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:10:01.806 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 147.3295ms
2025-05-29 20:10:01.820 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:10:01.831 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:01.929 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:01.946 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 209.2925ms
2025-05-29 20:10:02.013 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 682.0106ms
2025-05-29 20:10:02.029 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:10:02.134 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:10:02.176 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:02.196 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:02.212 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:10:02.252 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:10:02.284 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:10:02.318 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 81.9302ms
2025-05-29 20:10:02.347 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:02.376 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 242.3474ms
2025-05-29 20:10:11.317 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:10:11.317 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:10:11.319 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:10:11.319 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:10:11.427 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:11.435 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:11.451 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:11.443 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:11.529 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 212.4501ms
2025-05-29 20:10:11.532 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 215.1237ms
2025-05-29 20:10:11.537 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 217.5639ms
2025-05-29 20:10:11.540 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 221.6937ms
2025-05-29 20:10:11.642 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:10:11.633 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:10:11.710 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:11.716 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:11.745 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:10:11.747 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:11.776 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:10:11.778 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:10:11.829 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:10:11.841 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:10:11.912 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:10:11.927 +05:30 [INF] Executed DbCommand (21ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:10:11.952 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 120.2024ms
2025-05-29 20:10:11.959 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:10:11.995 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:11.999 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 176.7535ms
2025-05-29 20:10:12.063 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 429.5508ms
2025-05-29 20:10:12.066 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:10:12.071 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:10:12.168 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 526.2013ms
2025-05-29 20:10:12.175 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:12.178 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:10:12.252 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:12.258 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:10:12.291 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:10:12.294 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:10:12.350 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:10:12.353 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:10:12.403 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:10:12.412 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:10:12.455 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 109.0997ms
2025-05-29 20:10:12.467 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:10:12.498 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:10:12.506 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:10:12.547 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 475.9569ms
2025-05-29 20:10:12.556 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 148.2482ms
2025-05-29 20:10:12.614 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:10:12.631 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 452.713ms
2025-05-29 20:11:46.441 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-29 20:11:46.477 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:11:46.496 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 55.6537ms
2025-05-29 20:11:46.531 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/leads - application/json 353
2025-05-29 20:11:46.556 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:11:46.577 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)'
2025-05-29 20:11:46.610 +05:30 [INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:11:46.732 +05:30 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-05-29 20:11:46.791 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime2), @p5='?' (Size = 100), @p6='?' (Size = 50), @p7='?' (Size = 15), @p8='?' (DbType = DateTime2), @p9='?' (Size = 500), @p10='?' (Size = 1000), @p11='?' (DbType = Int32), @p12='?' (DbType = DateTime2), @p13='?' (DbType = DateTime2), @p14='?' (Size = 20), @p15='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Leads] ([ApprovedDate], [AssignedDate], [AssignedTo], [CreatedBy], [CreatedDate], [CustomerName], [LoanType], [MobileNumber], [RejectedDate], [RejectionReason], [ReviewComments], [ReviewedBy], [ReviewedDate], [StartedDate], [Status], [SubmittedDate])
OUTPUT INSERTED.[LeadId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-05-29 20:11:46.879 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__Leads__LoanType__5629CD9C". The conflict occurred in database "UBI_CPV_DB", table "dbo.Leads", column 'LoanType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:75653968-5a40-4775-afba-df9b066df078
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__Leads__LoanType__5629CD9C". The conflict occurred in database "UBI_CPV_DB", table "dbo.Leads", column 'LoanType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:75653968-5a40-4775-afba-df9b066df078
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-05-29 20:11:47.472 +05:30 [ERR] Error creating lead
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__Leads__LoanType__5629CD9C". The conflict occurred in database "UBI_CPV_DB", table "dbo.Leads", column 'LoanType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:75653968-5a40-4775-afba-df9b066df078
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.LeadsController.CreateLead(CreateLeadDto createLeadDto) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\LeadsController.cs:line 316
2025-05-29 20:11:47.596 +05:30 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-29 20:11:47.656 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 1004.0897ms
2025-05-29 20:11:47.752 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)'
2025-05-29 20:11:47.828 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/leads - 500 null application/json; charset=utf-8 1296.4993ms
2025-05-29 20:12:09.481 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-29 20:12:09.511 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:12:09.552 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 71.6886ms
2025-05-29 20:12:09.607 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/leads - application/json 353
2025-05-29 20:12:09.641 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:12:09.658 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)'
2025-05-29 20:12:09.674 +05:30 [INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:12:14.261 +05:30 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-05-29 20:12:14.372 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime2), @p5='?' (Size = 100), @p6='?' (Size = 50), @p7='?' (Size = 15), @p8='?' (DbType = DateTime2), @p9='?' (Size = 500), @p10='?' (Size = 1000), @p11='?' (DbType = Int32), @p12='?' (DbType = DateTime2), @p13='?' (DbType = DateTime2), @p14='?' (Size = 20), @p15='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Leads] ([ApprovedDate], [AssignedDate], [AssignedTo], [CreatedBy], [CreatedDate], [CustomerName], [LoanType], [MobileNumber], [RejectedDate], [RejectionReason], [ReviewComments], [ReviewedBy], [ReviewedDate], [StartedDate], [Status], [SubmittedDate])
OUTPUT INSERTED.[LeadId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-05-29 20:12:14.501 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__Leads__LoanType__5629CD9C". The conflict occurred in database "UBI_CPV_DB", table "dbo.Leads", column 'LoanType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:75653968-5a40-4775-afba-df9b066df078
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__Leads__LoanType__5629CD9C". The conflict occurred in database "UBI_CPV_DB", table "dbo.Leads", column 'LoanType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:75653968-5a40-4775-afba-df9b066df078
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-05-29 20:12:46.042 +05:30 [ERR] Error creating lead
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__Leads__LoanType__5629CD9C". The conflict occurred in database "UBI_CPV_DB", table "dbo.Leads", column 'LoanType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:75653968-5a40-4775-afba-df9b066df078
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.LeadsController.CreateLead(CreateLeadDto createLeadDto) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\LeadsController.cs:line 316
2025-05-29 20:12:46.125 +05:30 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-29 20:12:46.158 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 36459.1014ms
2025-05-29 20:12:46.176 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)'
2025-05-29 20:12:46.191 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/leads - 500 null application/json; charset=utf-8 36583.4304ms
2025-05-29 20:13:20.960 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-29 20:13:20.995 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:21.026 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 66.2656ms
2025-05-29 20:13:21.069 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/leads - application/json 357
2025-05-29 20:13:21.089 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:21.116 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)'
2025-05-29 20:13:21.138 +05:30 [INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:13:23.222 +05:30 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-05-29 20:13:23.258 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime2), @p5='?' (Size = 100), @p6='?' (Size = 50), @p7='?' (Size = 15), @p8='?' (DbType = DateTime2), @p9='?' (Size = 500), @p10='?' (Size = 1000), @p11='?' (DbType = Int32), @p12='?' (DbType = DateTime2), @p13='?' (DbType = DateTime2), @p14='?' (Size = 20), @p15='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Leads] ([ApprovedDate], [AssignedDate], [AssignedTo], [CreatedBy], [CreatedDate], [CustomerName], [LoanType], [MobileNumber], [RejectedDate], [RejectionReason], [ReviewComments], [ReviewedBy], [ReviewedDate], [StartedDate], [Status], [SubmittedDate])
OUTPUT INSERTED.[LeadId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-05-29 20:13:23.311 +05:30 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-05-29 20:13:23.376 +05:30 [INF] Executed DbCommand (42ms) [Parameters=[@p0='?' (Size = 500), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (Size = 50), @p4='?' (Size = 200), @p5='?' (DbType = Int32), @p6='?' (Size = 10), @p7='?' (Size = 50), @p8='?' (Size = 500), @p9='?' (Size = 50), @p10='?' (DbType = DateTime2), @p11='?' (Size = 50), @p12='?' (Size = 200), @p13='?' (DbType = Int32), @p14='?' (Size = 10), @p15='?' (Size = 50), @p16='?' (Size = 500), @p17='?' (DbType = Int32), @p18='?' (Size = 20), @p19='?' (DbType = DateTime2), @p20='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [LeadAddresses] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, 1)) AS i ([Address], [AddressType], [CreatedDate], [District], [Landmark], [LeadId], [Pincode], [State], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([Address], [AddressType], [CreatedDate], [District], [Landmark], [LeadId], [Pincode], [State])
VALUES (i.[Address], i.[AddressType], i.[CreatedDate], i.[District], i.[Landmark], i.[LeadId], i.[Pincode], i.[State])
OUTPUT INSERTED.[AddressId], i._Position;
INSERT INTO [LeadStatusHistory] ([Comments], [LeadId], [Status], [Timestamp], [UpdatedBy])
OUTPUT INSERTED.[HistoryId]
VALUES (@p16, @p17, @p18, @p19, @p20);
2025-05-29 20:13:23.463 +05:30 [INF] Executed DbCommand (56ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:13:25.476 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:13:25.510 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 4347.4598ms
2025-05-29 20:13:25.535 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)'
2025-05-29 20:13:25.553 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/leads - 200 null application/json; charset=utf-8 4484.5536ms
2025-05-29 20:13:27.969 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:13:27.970 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:13:27.972 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:13:27.970 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:13:28.136 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.151 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.170 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.185 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.272 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 301.8545ms
2025-05-29 20:13:28.269 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 300.4865ms
2025-05-29 20:13:28.281 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 309.0217ms
2025-05-29 20:13:28.304 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 333.9461ms
2025-05-29 20:13:28.335 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:13:28.355 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:13:28.391 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.396 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.416 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:13:28.418 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:13:28.435 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:13:28.439 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:13:28.482 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:13:28.488 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:13:28.528 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:13:28.551 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 74.7268ms
2025-05-29 20:13:28.577 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:13:28.558 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:13:28.601 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:13:28.598 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:13:28.591 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 255.2549ms
2025-05-29 20:13:28.657 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.660 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 177.0245ms
2025-05-29 20:13:28.692 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:13:28.695 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:13:28.718 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:13:28.729 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:13:28.743 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:13:28.719 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 364.3696ms
2025-05-29 20:13:28.793 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:28.798 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:13:28.837 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:13:28.839 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 99.5141ms
2025-05-29 20:13:28.872 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:13:28.875 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:13:28.919 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:13:28.919 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 318.7308ms
2025-05-29 20:13:28.976 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:13:29.022 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:13:29.041 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 126.6827ms
2025-05-29 20:13:29.056 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:13:29.074 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 344.9891ms
2025-05-29 20:13:34.596 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-05-29 20:13:34.597 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-05-29 20:13:34.680 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:34.692 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:34.739 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 142.5577ms
2025-05-29 20:13:34.741 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 144.2089ms
2025-05-29 20:13:34.819 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-05-29 20:13:34.929 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:34.946 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:13:34.972 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:13:35.036 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:13:39.938 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:13:40.000 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 4968.7194ms
2025-05-29 20:13:40.019 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:13:40.045 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 5226.2154ms
2025-05-29 20:13:40.055 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-05-29 20:13:40.094 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:13:40.108 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:13:40.121 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:13:40.148 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:13:40.181 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:13:40.203 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 61.5431ms
2025-05-29 20:13:40.221 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:13:40.236 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 180.6014ms
2025-05-29 20:14:20.973 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:14:20.988 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:14:21.001 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:14:21.026 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-05-29 20:14:21.138 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:21.144 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:21.151 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:21.157 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:21.222 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 249.2355ms
2025-05-29 20:14:21.237 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 248.8761ms
2025-05-29 20:14:21.294 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 293.0698ms
2025-05-29 20:14:21.298 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 271.3416ms
2025-05-29 20:14:21.312 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:14:21.324 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:14:21.460 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:21.469 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:21.548 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:21.552 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 227.5444ms
2025-05-29 20:14:21.565 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-05-29 20:14:21.613 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:21.624 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:14:21.632 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:21.696 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:14:21.702 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 137.5876ms
2025-05-29 20:14:21.759 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-05-29 20:14:21.782 +05:30 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:14:21.814 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:14:21.864 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 174.6777ms
2025-05-29 20:14:21.891 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:21.913 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 600.5541ms
2025-05-29 20:14:21.925 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-05-29 20:14:21.984 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:22.001 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:22.017 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:22.056 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:14:22.106 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:14:22.151 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:14:22.179 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 128.5707ms
2025-05-29 20:14:22.204 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:22.232 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 306.8524ms
2025-05-29 20:14:30.328 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-05-29 20:14:30.329 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-05-29 20:14:30.400 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:30.406 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:30.434 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 106.4665ms
2025-05-29 20:14:30.436 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 108.1732ms
2025-05-29 20:14:30.537 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-05-29 20:14:30.609 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:30.623 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:14:30.636 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:30.709 +05:30 [INF] Executed DbCommand (50ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:14:30.789 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:14:30.827 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 171.2963ms
2025-05-29 20:14:30.845 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:14:30.861 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 323.1707ms
2025-05-29 20:14:30.872 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-05-29 20:14:30.921 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:30.939 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:14:30.955 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:30.990 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-05-29 20:14:31.049 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-05-29 20:14:31.072 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 90.5682ms
2025-05-29 20:14:31.090 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-05-29 20:14:31.115 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 242.8382ms
2025-05-29 20:14:37.647 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:14:37.647 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:14:37.648 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:14:37.648 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:14:37.778 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:37.761 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:37.805 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:37.787 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:38.002 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 355.1768ms
2025-05-29 20:14:37.997 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 350.4234ms
2025-05-29 20:14:38.052 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 403.8079ms
2025-05-29 20:14:38.125 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:14:38.093 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 444.9341ms
2025-05-29 20:14:38.200 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:14:38.267 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:38.282 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:38.321 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:14:38.324 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:38.360 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:38.362 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:38.423 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:14:38.432 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:14:38.486 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:14:38.520 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:14:38.562 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 146.8048ms
2025-05-29 20:14:38.568 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:14:38.609 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:14:38.612 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 186.2201ms
2025-05-29 20:14:38.642 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 517.0555ms
2025-05-29 20:14:38.648 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:38.652 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-05-29 20:14:38.777 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 576.9762ms
2025-05-29 20:14:38.784 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:38.788 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-29 20:14:38.855 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:14:38.862 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:38.891 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:38.893 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:38.944 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:14:38.952 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:14:39.011 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-05-29 20:14:39.022 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-05-29 20:14:39.064 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 130.2748ms
2025-05-29 20:14:39.072 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:14:39.103 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-05-29 20:14:39.108 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:14:39.140 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 488.9941ms
2025-05-29 20:14:39.146 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 129.3219ms
2025-05-29 20:14:39.206 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:14:39.226 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 438.0332ms
2025-05-29 20:14:39.526 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - null null
2025-05-29 20:14:39.544 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:39.562 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - 204 null null 36.5685ms
2025-05-29 20:14:39.611 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/logout - null 0
2025-05-29 20:14:39.667 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:14:39.686 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-29 20:14:39.707 +05:30 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-29 20:14:39.822 +05:30 [INF] Executed DbCommand (49ms) [Parameters=[@__token_0='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[SessionId], [u].[CreatedAt], [u].[ExpiresAt], [u].[IsActive], [u].[RefreshToken], [u].[Token], [u].[UserId]
FROM [UserSessions] AS [u]
WHERE [u].[Token] = @__token_0 AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:14:39.890 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [UserSessions] SET [IsActive] = @p0
OUTPUT 1
WHERE [SessionId] = @p1;
2025-05-29 20:14:39.937 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-29 20:14:39.990 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API) in 235.9859ms
2025-05-29 20:14:40.012 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-29 20:14:40.054 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/logout - 200 null application/json; charset=utf-8 442.5525ms
2025-05-29 20:15:18.417 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null
2025-05-29 20:15:18.503 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:18.540 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 123.0567ms
2025-05-29 20:15:18.597 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 55
2025-05-29 20:15:18.621 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:18.635 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:15:18.650 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-29 20:15:18.677 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:15:18.748 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'.
2025-05-29 20:15:18.787 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 117.918ms
2025-05-29 20:15:18.816 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:15:18.836 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 200 null application/json; charset=utf-8 239.4794ms
2025-05-29 20:15:33.136 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null
2025-05-29 20:15:33.166 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:33.191 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 54.5264ms
2025-05-29 20:15:33.250 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 56
2025-05-29 20:15:33.307 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:33.345 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:15:33.375 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-29 20:15:33.426 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:15:33.828 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7;
2025-05-29 20:15:33.873 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'.
2025-05-29 20:15:33.902 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 489.3567ms
2025-05-29 20:15:33.921 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:15:33.949 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 200 null application/json; charset=utf-8 699.2572ms
2025-05-29 20:15:34.030 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:34.031 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:34.030 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:34.031 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:34.145 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:34.139 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:34.155 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:34.165 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:34.225 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 204 null null 193.6378ms
2025-05-29 20:15:34.227 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 204 null null 197.3462ms
2025-05-29 20:15:34.230 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - 204 null null 200.1119ms
2025-05-29 20:15:34.233 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - 204 null null 201.4757ms
2025-05-29 20:15:34.352 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:34.369 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:34.430 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:34.437 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:34.488 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:34.486 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:34.541 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:34.551 +05:30 [INF] Route matched with {action = "GetAgentDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto]] GetAgentDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:15:34.632 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:34.639 +05:30 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-05-29 20:15:34.699 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [PendingLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [CompletedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads], COUNT(*) AS [TotalAssigned]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:15:34.725 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:34.730 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto'.
2025-05-29 20:15:34.769 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:34.775 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API) in 153.0479ms
2025-05-29 20:15:34.814 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 197.3808ms
2025-05-29 20:15:34.817 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:34.853 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:34.856 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - 200 null application/json; charset=utf-8 486.8887ms
2025-05-29 20:15:34.866 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:34.916 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 200 null application/json; charset=utf-8 563.9163ms
2025-05-29 20:15:34.926 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:34.937 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:35.082 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:35.088 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:35.166 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:35.171 +05:30 [INF] Route matched with {action = "GetAgentDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto]] GetAgentDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:15:35.224 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:35.256 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [PendingLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [CompletedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads], COUNT(*) AS [TotalAssigned]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:15:35.314 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:35.317 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto'.
2025-05-29 20:15:35.369 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API) in 134.0755ms
2025-05-29 20:15:35.388 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:35.390 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:35.435 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - 200 null application/json; charset=utf-8 569.4864ms
2025-05-29 20:15:35.445 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:35.500 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 196.3077ms
2025-05-29 20:15:35.520 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:35.539 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 200 null application/json; charset=utf-8 613.3005ms
2025-05-29 20:15:38.158 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:38.158 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:38.232 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:38.239 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:38.271 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 204 null null 113.2954ms
2025-05-29 20:15:38.274 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 204 null null 115.9238ms
2025-05-29 20:15:38.322 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:38.352 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:38.368 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:38.384 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:38.420 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0
2025-05-29 20:15:38.483 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:38.508 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:38.526 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 116.5299ms
2025-05-29 20:15:38.545 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:38.562 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 200 null application/json; charset=utf-8 239.9449ms
2025-05-29 20:15:38.571 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:38.609 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:38.625 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:38.640 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:38.671 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0
2025-05-29 20:15:38.717 +05:30 [INF] Executed DbCommand (23ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:38.742 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:38.765 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 100.5127ms
2025-05-29 20:15:38.787 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:38.808 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 200 null application/json; charset=utf-8 236.6356ms
2025-05-29 20:15:42.115 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - null null
2025-05-29 20:15:42.115 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - null null
2025-05-29 20:15:42.216 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:42.234 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:42.296 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - 204 null null 181.1062ms
2025-05-29 20:15:42.301 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - 204 null null 186.1048ms
2025-05-29 20:15:42.373 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - null null
2025-05-29 20:15:42.428 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:42.458 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:42.493 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:42.567 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:42.655 +05:30 [INF] Executed DbCommand (33ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:42.701 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:42.730 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 182.0732ms
2025-05-29 20:15:42.754 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:42.781 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - 200 null application/json; charset=utf-8 408.0818ms
2025-05-29 20:15:42.793 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - null null
2025-05-29 20:15:42.851 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=rejected - null null
2025-05-29 20:15:42.858 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:42.934 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:42.938 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:42.994 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=rejected - 204 null null 143.1821ms
2025-05-29 20:15:42.999 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:43.077 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=rejected - null null
2025-05-29 20:15:43.102 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:43.170 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:43.206 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:43.218 +05:30 [INF] Executed DbCommand (36ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:43.266 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:43.274 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:43.375 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 289.6755ms
2025-05-29 20:15:43.376 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:43.457 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:43.510 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=approved - 200 null application/json; charset=utf-8 716.5448ms
2025-05-29 20:15:43.514 +05:30 [INF] Executed DbCommand (41ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:43.606 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:43.644 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 283.2459ms
2025-05-29 20:15:43.668 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:43.690 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=rejected - 200 null application/json; charset=utf-8 612.8034ms
2025-05-29 20:15:43.701 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=rejected - null null
2025-05-29 20:15:43.759 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - null null
2025-05-29 20:15:43.772 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:43.825 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:43.829 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:43.873 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - 204 null null 114.1526ms
2025-05-29 20:15:43.877 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:43.932 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - null null
2025-05-29 20:15:43.949 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:44.000 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:44.039 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:44.044 +05:30 [INF] Executed DbCommand (33ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:44.109 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:44.121 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:44.224 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 287.4393ms
2025-05-29 20:15:44.228 +05:30 [INF] Executed DbCommand (18ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:44.291 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:44.325 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=rejected - 200 null application/json; charset=utf-8 624.8982ms
2025-05-29 20:15:44.334 +05:30 [INF] Executed DbCommand (31ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:44.416 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:44.464 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 256.5144ms
2025-05-29 20:15:44.497 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:44.521 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - 200 null application/json; charset=utf-8 588.527ms
2025-05-29 20:15:44.533 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - null null
2025-05-29 20:15:44.599 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:44.620 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:44.645 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:44.695 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:44.769 +05:30 [INF] Executed DbCommand (31ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:44.809 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:44.844 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 162.8684ms
2025-05-29 20:15:44.888 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:44.935 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - 200 null application/json; charset=utf-8 401.6794ms
2025-05-29 20:15:46.405 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:46.405 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:46.492 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:46.510 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:46.570 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - 204 null null 165.3403ms
2025-05-29 20:15:46.577 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - 204 null null 171.9308ms
2025-05-29 20:15:46.659 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:46.712 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:46.739 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:46.771 +05:30 [INF] Route matched with {action = "GetAgentDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto]] GetAgentDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:15:46.827 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [PendingLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [CompletedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads], COUNT(*) AS [TotalAssigned]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:15:46.864 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto'.
2025-05-29 20:15:46.892 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API) in 77.5926ms
2025-05-29 20:15:46.915 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:46.937 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - 200 null application/json; charset=utf-8 278.7053ms
2025-05-29 20:15:46.947 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:47.010 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:47.037 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:47.066 +05:30 [INF] Route matched with {action = "GetAgentDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto]] GetAgentDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:15:47.129 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [PendingLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [CompletedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads], COUNT(*) AS [TotalAssigned]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:15:47.158 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto'.
2025-05-29 20:15:47.185 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API) in 67.0858ms
2025-05-29 20:15:47.210 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:47.237 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - 200 null application/json; charset=utf-8 289.6319ms
2025-05-29 20:15:52.432 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:52.432 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:52.493 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:52.507 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:52.560 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 204 null null 127.7175ms
2025-05-29 20:15:52.565 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 204 null null 132.5716ms
2025-05-29 20:15:52.623 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:52.660 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:52.680 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:52.698 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:52.737 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0
2025-05-29 20:15:52.803 +05:30 [INF] Executed DbCommand (36ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:52.850 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:52.881 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 155.4375ms
2025-05-29 20:15:52.907 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:52.928 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 200 null application/json; charset=utf-8 305.0831ms
2025-05-29 20:15:52.938 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - null null
2025-05-29 20:15:53.016 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:53.042 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:53.072 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:53.125 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0
2025-05-29 20:15:53.193 +05:30 [INF] Executed DbCommand (29ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:53.237 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:53.264 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 148.9005ms
2025-05-29 20:15:53.289 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:53.310 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100 - 200 null application/json; charset=utf-8 372.3669ms
2025-05-29 20:15:53.508 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:53.511 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:53.511 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:53.512 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:53.687 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:53.699 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:53.714 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:53.728 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:53.811 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 204 null null 302.5474ms
2025-05-29 20:15:53.814 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - 204 null null 303.256ms
2025-05-29 20:15:53.817 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 204 null null 305.863ms
2025-05-29 20:15:53.821 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/agent/dashboard-stats - 204 null null 309.3111ms
2025-05-29 20:15:53.909 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:53.917 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:53.956 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:53.998 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:54.001 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:54.032 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:54.034 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:54.089 +05:30 [INF] Route matched with {action = "GetAgentDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto]] GetAgentDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:15:54.112 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:54.179 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [PendingLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [CompletedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads], COUNT(*) AS [TotalAssigned]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:15:54.207 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:54.211 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto'.
2025-05-29 20:15:54.268 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:54.274 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API) in 107.0547ms
2025-05-29 20:15:54.334 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 236.8644ms
2025-05-29 20:15:54.339 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:54.376 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:54.379 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - 200 null application/json; charset=utf-8 462.0975ms
2025-05-29 20:15:54.387 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - null null
2025-05-29 20:15:54.431 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 200 null application/json; charset=utf-8 521.5406ms
2025-05-29 20:15:54.439 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - null null
2025-05-29 20:15:54.444 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:54.513 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:54.515 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:54.550 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:54.553 +05:30 [INF] Route matched with {action = "GetAgentDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto]] GetAgentDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:15:54.599 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:15:54.618 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [PendingLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [CompletedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads], COUNT(*) AS [TotalAssigned]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:15:54.682 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.AgentDashboardStatsDto'.
2025-05-29 20:15:54.683 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
2025-05-29 20:15:54.732 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API) in 128.6896ms
2025-05-29 20:15:54.757 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetAgentDashboardStats (UBI.CPV.API)'
2025-05-29 20:15:54.765 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__currentUserId_0='?' (DbType = Int32), @__status_1='?' (Size = 20), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[AssignedTo] = @__currentUserId_0 AND [l].[Status] = @__status_1
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:15:54.804 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/agent/dashboard-stats - 200 null application/json; charset=utf-8 417.7124ms
2025-05-29 20:15:54.812 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:15:54.883 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 212.507ms
2025-05-29 20:15:54.910 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:15:54.937 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=assigned - 200 null application/json; charset=utf-8 498.8034ms
2025-05-29 20:15:57.522 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - null null
2025-05-29 20:15:57.596 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:57.619 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - 204 null null 96.2997ms
2025-05-29 20:15:57.652 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/logout - null 0
2025-05-29 20:15:57.684 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:15:57.705 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-29 20:15:57.720 +05:30 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-29 20:15:57.773 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__token_0='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[SessionId], [u].[CreatedAt], [u].[ExpiresAt], [u].[IsActive], [u].[RefreshToken], [u].[Token], [u].[UserId]
FROM [UserSessions] AS [u]
WHERE [u].[Token] = @__token_0 AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:15:57.826 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [UserSessions] SET [IsActive] = @p0
OUTPUT 1
WHERE [SessionId] = @p1;
2025-05-29 20:15:57.860 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-29 20:15:57.888 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API) in 121.4911ms
2025-05-29 20:15:57.914 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-29 20:15:57.942 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/logout - 200 null application/json; charset=utf-8 290.1759ms
2025-05-29 20:16:06.945 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null
2025-05-29 20:16:06.978 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:06.995 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 50.0864ms
2025-05-29 20:16:07.025 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 66
2025-05-29 20:16:07.050 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:07.071 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:16:07.103 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-29 20:16:07.161 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:07.571 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7;
2025-05-29 20:16:07.604 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'.
2025-05-29 20:16:07.627 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 481.4212ms
2025-05-29 20:16:07.650 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-05-29 20:16:07.665 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 200 null application/json; charset=utf-8 640.3257ms
2025-05-29 20:16:07.736 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - null null
2025-05-29 20:16:07.736 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:07.737 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - null null
2025-05-29 20:16:07.737 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:07.854 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:07.869 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:07.876 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:07.861 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:07.947 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - 204 null null 210.6443ms
2025-05-29 20:16:07.973 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - 204 null null 236.0897ms
2025-05-29 20:16:07.975 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - 204 null null 238.219ms
2025-05-29 20:16:07.977 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - 204 null null 240.547ms
2025-05-29 20:16:07.987 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - null null
2025-05-29 20:16:08.083 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:08.099 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:08.134 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:08.137 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:08.178 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:08.182 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:16:08.242 +05:30 [INF] Route matched with {action = "GetSupervisorDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto]] GetSupervisorDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:16:08.256 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__status_0='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[Status] = @__status_0
2025-05-29 20:16:08.340 +05:30 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-05-29 20:16:08.357 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__status_0='?' (Size = 20), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[Status] = @__status_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:16:08.406 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:16:08.434 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__today_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviews], COUNT(CASE
    WHEN [t].[Status] = N'approved' AND [t].[ApprovedDate] IS NOT NULL AND CONVERT(date, [t].[ApprovedDate]) = @__today_0 THEN 1
END) AS [ApprovedToday], COUNT(CASE
    WHEN [t].[Status] = N'rejected' AND [t].[RejectedDate] IS NOT NULL AND CONVERT(date, [t].[RejectedDate]) = @__today_0 THEN 1
END) AS [RejectedToday], COUNT(CASE
    WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
END) AS [TotalReviewed], CASE
    WHEN COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) > 0 THEN (CAST(COUNT(CASE
        WHEN [t].[Status] = N'approved' THEN 1
    END) AS decimal(18,2)) / CAST(COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) AS decimal(18,2))) * 100.0
    ELSE 0.0
END AS [ApprovalRate]
FROM (
    SELECT [l].[ApprovedDate], [l].[RejectedDate], [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:16:08.436 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 194.4956ms
2025-05-29 20:16:08.483 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:08.502 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - 200 null application/json; charset=utf-8 515.0218ms
2025-05-29 20:16:08.511 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - null null
2025-05-29 20:16:08.552 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:08.571 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:08.574 +05:30 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[UserId], [u].[FirstName], [u].[LastName], (
    SELECT COUNT(*)
    FROM [Leads] AS [l]
    WHERE [u].[UserId] = [l].[AssignedTo]), (
    SELECT COUNT(*)
    FROM [Leads] AS [l0]
    WHERE [u].[UserId] = [l0].[AssignedTo] AND [l0].[Status] IN (N'approved', N'rejected')), (
    SELECT COUNT(*)
    FROM [Leads] AS [l1]
    WHERE [u].[UserId] = [l1].[AssignedTo] AND [l1].[Status] = N'approved'), (
    SELECT COUNT(*)
    FROM [Leads] AS [l2]
    WHERE [u].[UserId] = [l2].[AssignedTo] AND [l2].[Status] = N'rejected'), CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l3]
        WHERE [u].[UserId] = [l3].[AssignedTo]) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l4]
        WHERE [u].[UserId] = [l4].[AssignedTo] AND [l4].[Status] IN (N'approved', N'rejected')) AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l5]
        WHERE [u].[UserId] = [l5].[AssignedTo]) AS decimal(18,2))) * 100.0
    ELSE 0.0
END, CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l6]
        WHERE [u].[UserId] = [l6].[AssignedTo] AND [l6].[Status] IN (N'approved', N'rejected')) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l7]
        WHERE [u].[UserId] = [l7].[AssignedTo] AND [l7].[Status] = N'approved') AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l8]
        WHERE [u].[UserId] = [l8].[AssignedTo] AND [l8].[Status] IN (N'approved', N'rejected')) AS decimal(18,2))) * 100.0
    ELSE 0.0
END
FROM [Users] AS [u]
WHERE [u].[Role] = N'Agent' AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:08.608 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:16:08.616 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto'.
2025-05-29 20:16:08.657 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__status_0='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[Status] = @__status_0
2025-05-29 20:16:08.673 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API) in 366.6951ms
2025-05-29 20:16:08.703 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:08.719 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - 200 null application/json; charset=utf-8 636.2894ms
2025-05-29 20:16:08.727 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:08.729 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__status_0='?' (Size = 20), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[Status] = @__status_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:16:08.781 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:08.792 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:16:08.824 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:08.827 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 175.0052ms
2025-05-29 20:16:08.856 +05:30 [INF] Route matched with {action = "GetSupervisorDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto]] GetSupervisorDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:16:08.859 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:08.907 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=50&status=pending-review - 200 null application/json; charset=utf-8 396.6011ms
2025-05-29 20:16:08.909 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__today_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviews], COUNT(CASE
    WHEN [t].[Status] = N'approved' AND [t].[ApprovedDate] IS NOT NULL AND CONVERT(date, [t].[ApprovedDate]) = @__today_0 THEN 1
END) AS [ApprovedToday], COUNT(CASE
    WHEN [t].[Status] = N'rejected' AND [t].[RejectedDate] IS NOT NULL AND CONVERT(date, [t].[RejectedDate]) = @__today_0 THEN 1
END) AS [RejectedToday], COUNT(CASE
    WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
END) AS [TotalReviewed], CASE
    WHEN COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) > 0 THEN (CAST(COUNT(CASE
        WHEN [t].[Status] = N'approved' THEN 1
    END) AS decimal(18,2)) / CAST(COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) AS decimal(18,2))) * 100.0
    ELSE 0.0
END AS [ApprovalRate]
FROM (
    SELECT [l].[ApprovedDate], [l].[RejectedDate], [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:16:09.030 +05:30 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[UserId], [u].[FirstName], [u].[LastName], (
    SELECT COUNT(*)
    FROM [Leads] AS [l]
    WHERE [u].[UserId] = [l].[AssignedTo]), (
    SELECT COUNT(*)
    FROM [Leads] AS [l0]
    WHERE [u].[UserId] = [l0].[AssignedTo] AND [l0].[Status] IN (N'approved', N'rejected')), (
    SELECT COUNT(*)
    FROM [Leads] AS [l1]
    WHERE [u].[UserId] = [l1].[AssignedTo] AND [l1].[Status] = N'approved'), (
    SELECT COUNT(*)
    FROM [Leads] AS [l2]
    WHERE [u].[UserId] = [l2].[AssignedTo] AND [l2].[Status] = N'rejected'), CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l3]
        WHERE [u].[UserId] = [l3].[AssignedTo]) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l4]
        WHERE [u].[UserId] = [l4].[AssignedTo] AND [l4].[Status] IN (N'approved', N'rejected')) AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l5]
        WHERE [u].[UserId] = [l5].[AssignedTo]) AS decimal(18,2))) * 100.0
    ELSE 0.0
END, CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l6]
        WHERE [u].[UserId] = [l6].[AssignedTo] AND [l6].[Status] IN (N'approved', N'rejected')) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l7]
        WHERE [u].[UserId] = [l7].[AssignedTo] AND [l7].[Status] = N'approved') AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l8]
        WHERE [u].[UserId] = [l8].[AssignedTo] AND [l8].[Status] IN (N'approved', N'rejected')) AS decimal(18,2))) * 100.0
    ELSE 0.0
END
FROM [Users] AS [u]
WHERE [u].[Role] = N'Agent' AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:09.066 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto'.
2025-05-29 20:16:09.100 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API) in 201.2183ms
2025-05-29 20:16:09.123 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:09.146 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - 200 null application/json; charset=utf-8 418.3357ms
2025-05-29 20:16:12.751 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - null null
2025-05-29 20:16:12.751 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - null null
2025-05-29 20:16:12.805 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:12.812 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:12.841 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - 204 null null 90.4774ms
2025-05-29 20:16:12.844 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - 204 null null 92.9012ms
2025-05-29 20:16:12.900 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - null null
2025-05-29 20:16:12.933 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:12.951 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:12.967 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:16:12.998 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__status_0='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[Status] = @__status_0
2025-05-29 20:16:13.060 +05:30 [INF] Executed DbCommand (36ms) [Parameters=[@__status_0='?' (Size = 20), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[Status] = @__status_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:16:13.091 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:16:13.119 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 127.6502ms
2025-05-29 20:16:13.141 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:13.161 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - 200 null application/json; charset=utf-8 261.2629ms
2025-05-29 20:16:13.171 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - null null
2025-05-29 20:16:13.268 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:13.303 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:13.343 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-05-29 20:16:13.410 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__status_0='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
WHERE [l].[Status] = @__status_0
2025-05-29 20:16:13.496 +05:30 [INF] Executed DbCommand (31ms) [Parameters=[@__status_0='?' (Size = 20), @__p_1='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    WHERE [l].[Status] = @__status_0
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_1 ROWS FETCH NEXT @__p_2 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-05-29 20:16:13.551 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 20:16:13.583 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 183.5792ms
2025-05-29 20:16:13.606 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-05-29 20:16:13.629 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=100&status=pending-review - 200 null application/json; charset=utf-8 457.5719ms
2025-05-29 20:16:15.941 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:15.941 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:16.006 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:16.015 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:16.065 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - 204 null null 123.4407ms
2025-05-29 20:16:16.070 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - 204 null null 128.2826ms
2025-05-29 20:16:16.125 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:16.161 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:16.177 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:16.190 +05:30 [INF] Route matched with {action = "GetSupervisorDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto]] GetSupervisorDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:16:16.228 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__today_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviews], COUNT(CASE
    WHEN [t].[Status] = N'approved' AND [t].[ApprovedDate] IS NOT NULL AND CONVERT(date, [t].[ApprovedDate]) = @__today_0 THEN 1
END) AS [ApprovedToday], COUNT(CASE
    WHEN [t].[Status] = N'rejected' AND [t].[RejectedDate] IS NOT NULL AND CONVERT(date, [t].[RejectedDate]) = @__today_0 THEN 1
END) AS [RejectedToday], COUNT(CASE
    WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
END) AS [TotalReviewed], CASE
    WHEN COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) > 0 THEN (CAST(COUNT(CASE
        WHEN [t].[Status] = N'approved' THEN 1
    END) AS decimal(18,2)) / CAST(COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) AS decimal(18,2))) * 100.0
    ELSE 0.0
END AS [ApprovalRate]
FROM (
    SELECT [l].[ApprovedDate], [l].[RejectedDate], [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:16:16.327 +05:30 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[UserId], [u].[FirstName], [u].[LastName], (
    SELECT COUNT(*)
    FROM [Leads] AS [l]
    WHERE [u].[UserId] = [l].[AssignedTo]), (
    SELECT COUNT(*)
    FROM [Leads] AS [l0]
    WHERE [u].[UserId] = [l0].[AssignedTo] AND [l0].[Status] IN (N'approved', N'rejected')), (
    SELECT COUNT(*)
    FROM [Leads] AS [l1]
    WHERE [u].[UserId] = [l1].[AssignedTo] AND [l1].[Status] = N'approved'), (
    SELECT COUNT(*)
    FROM [Leads] AS [l2]
    WHERE [u].[UserId] = [l2].[AssignedTo] AND [l2].[Status] = N'rejected'), CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l3]
        WHERE [u].[UserId] = [l3].[AssignedTo]) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l4]
        WHERE [u].[UserId] = [l4].[AssignedTo] AND [l4].[Status] IN (N'approved', N'rejected')) AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l5]
        WHERE [u].[UserId] = [l5].[AssignedTo]) AS decimal(18,2))) * 100.0
    ELSE 0.0
END, CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l6]
        WHERE [u].[UserId] = [l6].[AssignedTo] AND [l6].[Status] IN (N'approved', N'rejected')) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l7]
        WHERE [u].[UserId] = [l7].[AssignedTo] AND [l7].[Status] = N'approved') AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l8]
        WHERE [u].[UserId] = [l8].[AssignedTo] AND [l8].[Status] IN (N'approved', N'rejected')) AS decimal(18,2))) * 100.0
    ELSE 0.0
END
FROM [Users] AS [u]
WHERE [u].[Role] = N'Agent' AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:16.368 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto'.
2025-05-29 20:16:16.399 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API) in 183.263ms
2025-05-29 20:16:16.429 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:16.460 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - 200 null application/json; charset=utf-8 335.2257ms
2025-05-29 20:16:16.552 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:16.580 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:16.602 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:16.623 +05:30 [INF] Route matched with {action = "GetSupervisorDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto]] GetSupervisorDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:16:16.678 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__today_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviews], COUNT(CASE
    WHEN [t].[Status] = N'approved' AND [t].[ApprovedDate] IS NOT NULL AND CONVERT(date, [t].[ApprovedDate]) = @__today_0 THEN 1
END) AS [ApprovedToday], COUNT(CASE
    WHEN [t].[Status] = N'rejected' AND [t].[RejectedDate] IS NOT NULL AND CONVERT(date, [t].[RejectedDate]) = @__today_0 THEN 1
END) AS [RejectedToday], COUNT(CASE
    WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
END) AS [TotalReviewed], CASE
    WHEN COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) > 0 THEN (CAST(COUNT(CASE
        WHEN [t].[Status] = N'approved' THEN 1
    END) AS decimal(18,2)) / CAST(COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) AS decimal(18,2))) * 100.0
    ELSE 0.0
END AS [ApprovalRate]
FROM (
    SELECT [l].[ApprovedDate], [l].[RejectedDate], [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:16:16.809 +05:30 [INF] Executed DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[UserId], [u].[FirstName], [u].[LastName], (
    SELECT COUNT(*)
    FROM [Leads] AS [l]
    WHERE [u].[UserId] = [l].[AssignedTo]), (
    SELECT COUNT(*)
    FROM [Leads] AS [l0]
    WHERE [u].[UserId] = [l0].[AssignedTo] AND [l0].[Status] IN (N'approved', N'rejected')), (
    SELECT COUNT(*)
    FROM [Leads] AS [l1]
    WHERE [u].[UserId] = [l1].[AssignedTo] AND [l1].[Status] = N'approved'), (
    SELECT COUNT(*)
    FROM [Leads] AS [l2]
    WHERE [u].[UserId] = [l2].[AssignedTo] AND [l2].[Status] = N'rejected'), CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l3]
        WHERE [u].[UserId] = [l3].[AssignedTo]) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l4]
        WHERE [u].[UserId] = [l4].[AssignedTo] AND [l4].[Status] IN (N'approved', N'rejected')) AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l5]
        WHERE [u].[UserId] = [l5].[AssignedTo]) AS decimal(18,2))) * 100.0
    ELSE 0.0
END, CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l6]
        WHERE [u].[UserId] = [l6].[AssignedTo] AND [l6].[Status] IN (N'approved', N'rejected')) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l7]
        WHERE [u].[UserId] = [l7].[AssignedTo] AND [l7].[Status] = N'approved') AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l8]
        WHERE [u].[UserId] = [l8].[AssignedTo] AND [l8].[Status] IN (N'approved', N'rejected')) AS decimal(18,2))) * 100.0
    ELSE 0.0
END
FROM [Users] AS [u]
WHERE [u].[Role] = N'Agent' AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:16.846 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto'.
2025-05-29 20:16:16.876 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API) in 212.0419ms
2025-05-29 20:16:16.908 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:16.944 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - 200 null application/json; charset=utf-8 392.0522ms
2025-05-29 20:16:25.162 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:25.213 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:25.220 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:25.308 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:25.311 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - 204 null null 149.492ms
2025-05-29 20:16:25.393 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/verification/supervisor/dashboard-stats - 204 null null 180.3324ms
2025-05-29 20:16:25.409 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:25.484 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:25.501 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:25.517 +05:30 [INF] Route matched with {action = "GetSupervisorDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto]] GetSupervisorDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:16:25.554 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__today_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviews], COUNT(CASE
    WHEN [t].[Status] = N'approved' AND [t].[ApprovedDate] IS NOT NULL AND CONVERT(date, [t].[ApprovedDate]) = @__today_0 THEN 1
END) AS [ApprovedToday], COUNT(CASE
    WHEN [t].[Status] = N'rejected' AND [t].[RejectedDate] IS NOT NULL AND CONVERT(date, [t].[RejectedDate]) = @__today_0 THEN 1
END) AS [RejectedToday], COUNT(CASE
    WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
END) AS [TotalReviewed], CASE
    WHEN COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) > 0 THEN (CAST(COUNT(CASE
        WHEN [t].[Status] = N'approved' THEN 1
    END) AS decimal(18,2)) / CAST(COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) AS decimal(18,2))) * 100.0
    ELSE 0.0
END AS [ApprovalRate]
FROM (
    SELECT [l].[ApprovedDate], [l].[RejectedDate], [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:16:25.640 +05:30 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[UserId], [u].[FirstName], [u].[LastName], (
    SELECT COUNT(*)
    FROM [Leads] AS [l]
    WHERE [u].[UserId] = [l].[AssignedTo]), (
    SELECT COUNT(*)
    FROM [Leads] AS [l0]
    WHERE [u].[UserId] = [l0].[AssignedTo] AND [l0].[Status] IN (N'approved', N'rejected')), (
    SELECT COUNT(*)
    FROM [Leads] AS [l1]
    WHERE [u].[UserId] = [l1].[AssignedTo] AND [l1].[Status] = N'approved'), (
    SELECT COUNT(*)
    FROM [Leads] AS [l2]
    WHERE [u].[UserId] = [l2].[AssignedTo] AND [l2].[Status] = N'rejected'), CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l3]
        WHERE [u].[UserId] = [l3].[AssignedTo]) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l4]
        WHERE [u].[UserId] = [l4].[AssignedTo] AND [l4].[Status] IN (N'approved', N'rejected')) AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l5]
        WHERE [u].[UserId] = [l5].[AssignedTo]) AS decimal(18,2))) * 100.0
    ELSE 0.0
END, CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l6]
        WHERE [u].[UserId] = [l6].[AssignedTo] AND [l6].[Status] IN (N'approved', N'rejected')) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l7]
        WHERE [u].[UserId] = [l7].[AssignedTo] AND [l7].[Status] = N'approved') AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l8]
        WHERE [u].[UserId] = [l8].[AssignedTo] AND [l8].[Status] IN (N'approved', N'rejected')) AS decimal(18,2))) * 100.0
    ELSE 0.0
END
FROM [Users] AS [u]
WHERE [u].[Role] = N'Agent' AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:25.687 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto'.
2025-05-29 20:16:25.710 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API) in 168.8425ms
2025-05-29 20:16:25.729 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:25.748 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - 200 null application/json; charset=utf-8 338.9728ms
2025-05-29 20:16:25.755 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - null null
2025-05-29 20:16:25.791 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:25.806 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:25.824 +05:30 [INF] Route matched with {action = "GetSupervisorDashboardStats", controller = "Verification"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto]] GetSupervisorDashboardStats() on controller UBI.CPV.API.Controllers.VerificationController (UBI.CPV.API).
2025-05-29 20:16:25.856 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__today_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviews], COUNT(CASE
    WHEN [t].[Status] = N'approved' AND [t].[ApprovedDate] IS NOT NULL AND CONVERT(date, [t].[ApprovedDate]) = @__today_0 THEN 1
END) AS [ApprovedToday], COUNT(CASE
    WHEN [t].[Status] = N'rejected' AND [t].[RejectedDate] IS NOT NULL AND CONVERT(date, [t].[RejectedDate]) = @__today_0 THEN 1
END) AS [RejectedToday], COUNT(CASE
    WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
END) AS [TotalReviewed], CASE
    WHEN COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) > 0 THEN (CAST(COUNT(CASE
        WHEN [t].[Status] = N'approved' THEN 1
    END) AS decimal(18,2)) / CAST(COUNT(CASE
        WHEN [t].[Status] IN (N'approved', N'rejected') THEN 1
    END) AS decimal(18,2))) * 100.0
    ELSE 0.0
END AS [ApprovalRate]
FROM (
    SELECT [l].[ApprovedDate], [l].[RejectedDate], [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-05-29 20:16:25.970 +05:30 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[UserId], [u].[FirstName], [u].[LastName], (
    SELECT COUNT(*)
    FROM [Leads] AS [l]
    WHERE [u].[UserId] = [l].[AssignedTo]), (
    SELECT COUNT(*)
    FROM [Leads] AS [l0]
    WHERE [u].[UserId] = [l0].[AssignedTo] AND [l0].[Status] IN (N'approved', N'rejected')), (
    SELECT COUNT(*)
    FROM [Leads] AS [l1]
    WHERE [u].[UserId] = [l1].[AssignedTo] AND [l1].[Status] = N'approved'), (
    SELECT COUNT(*)
    FROM [Leads] AS [l2]
    WHERE [u].[UserId] = [l2].[AssignedTo] AND [l2].[Status] = N'rejected'), CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l3]
        WHERE [u].[UserId] = [l3].[AssignedTo]) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l4]
        WHERE [u].[UserId] = [l4].[AssignedTo] AND [l4].[Status] IN (N'approved', N'rejected')) AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l5]
        WHERE [u].[UserId] = [l5].[AssignedTo]) AS decimal(18,2))) * 100.0
    ELSE 0.0
END, CASE
    WHEN (
        SELECT COUNT(*)
        FROM [Leads] AS [l6]
        WHERE [u].[UserId] = [l6].[AssignedTo] AND [l6].[Status] IN (N'approved', N'rejected')) > 0 THEN (CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l7]
        WHERE [u].[UserId] = [l7].[AssignedTo] AND [l7].[Status] = N'approved') AS decimal(18,2)) / CAST((
        SELECT COUNT(*)
        FROM [Leads] AS [l8]
        WHERE [u].[UserId] = [l8].[AssignedTo] AND [l8].[Status] IN (N'approved', N'rejected')) AS decimal(18,2))) * 100.0
    ELSE 0.0
END
FROM [Users] AS [u]
WHERE [u].[Role] = N'Agent' AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:26.012 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.SupervisorDashboardStatsDto'.
2025-05-29 20:16:26.039 +05:30 [INF] Executed action UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API) in 186.7926ms
2025-05-29 20:16:26.056 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.VerificationController.GetSupervisorDashboardStats (UBI.CPV.API)'
2025-05-29 20:16:26.075 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/verification/supervisor/dashboard-stats - 200 null application/json; charset=utf-8 319.2959ms
2025-05-29 20:16:28.340 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - null null
2025-05-29 20:16:28.403 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:28.445 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/logout - 204 null null 104.294ms
2025-05-29 20:16:28.497 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/logout - null 0
2025-05-29 20:16:28.533 +05:30 [INF] CORS policy execution successful.
2025-05-29 20:16:28.565 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-29 20:16:28.588 +05:30 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-05-29 20:16:28.623 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__token_0='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[SessionId], [u].[CreatedAt], [u].[ExpiresAt], [u].[IsActive], [u].[RefreshToken], [u].[Token], [u].[UserId]
FROM [UserSessions] AS [u]
WHERE [u].[Token] = @__token_0 AND [u].[IsActive] = CAST(1 AS bit)
2025-05-29 20:16:28.669 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [UserSessions] SET [IsActive] = @p0
OUTPUT 1
WHERE [SessionId] = @p1;
2025-05-29 20:16:28.695 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-29 20:16:28.718 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API) in 101.6167ms
2025-05-29 20:16:28.737 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Logout (UBI.CPV.API)'
2025-05-29 20:16:28.755 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/logout - 200 null application/json; charset=utf-8 258.4609ms
