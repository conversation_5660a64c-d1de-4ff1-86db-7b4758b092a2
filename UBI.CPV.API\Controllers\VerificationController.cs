using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using UBI.CPV.API.Data;
using UBI.CPV.API.Models.DTOs;
using UBI.CPV.API.Models.Entities;

namespace UBI.CPV.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class VerificationController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<VerificationController> _logger;

        public VerificationController(ApplicationDbContext context, ILogger<VerificationController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost("leads/{leadId}")]
        [Authorize(Roles = "Agent")]
        public async Task<ActionResult<VerificationDataDto>> CreateVerificationData(int leadId, [FromBody] CreateVerificationDataDto createDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                // Verify lead exists and is assigned to current agent
                var lead = await _context.Leads.FindAsync(leadId);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                if (lead.AssignedTo != currentUserId)
                {
                    return Forbid("You can only create verification data for leads assigned to you");
                }

                // Check if verification data already exists
                var existingVerification = await _context.VerificationData
                    .FirstOrDefaultAsync(vd => vd.LeadId == leadId);

                if (existingVerification != null)
                {
                    return BadRequest(new { Message = "Verification data already exists for this lead" });
                }

                // Create verification data
                var verificationData = new VerificationData
                {
                    LeadId = leadId,
                    AgentName = createDto.AgentName,
                    AgentContact = createDto.AgentContact,
                    AddressConfirmed = createDto.AddressConfirmed,
                    PersonMet = createDto.PersonMet,
                    Relationship = createDto.Relationship,
                    OfficeAddress = createDto.OfficeAddress,
                    OfficeState = createDto.OfficeState,
                    OfficeDistrict = createDto.OfficeDistrict,
                    OfficePincode = createDto.OfficePincode,
                    Landmark = createDto.Landmark,
                    CompanyType = createDto.CompanyType,
                    BusinessNature = createDto.BusinessNature,
                    EstablishmentYear = createDto.EstablishmentYear,
                    EmployeesCount = createDto.EmployeesCount,
                    GrossSalary = createDto.GrossSalary,
                    NetSalary = createDto.NetSalary,
                    ProofType = createDto.ProofType,
                    VerificationDate = DateTime.UtcNow,
                    AdditionalNotes = createDto.AdditionalNotes
                };

                _context.VerificationData.Add(verificationData);
                await _context.SaveChangesAsync();

                var resultDto = new VerificationDataDto
                {
                    VerificationId = verificationData.VerificationId,
                    LeadId = verificationData.LeadId,
                    AgentName = verificationData.AgentName,
                    AgentContact = verificationData.AgentContact,
                    AddressConfirmed = verificationData.AddressConfirmed,
                    PersonMet = verificationData.PersonMet,
                    Relationship = verificationData.Relationship,
                    OfficeAddress = verificationData.OfficeAddress,
                    OfficeState = verificationData.OfficeState,
                    OfficeDistrict = verificationData.OfficeDistrict,
                    OfficePincode = verificationData.OfficePincode,
                    Landmark = verificationData.Landmark,
                    CompanyType = verificationData.CompanyType,
                    BusinessNature = verificationData.BusinessNature,
                    EstablishmentYear = verificationData.EstablishmentYear,
                    EmployeesCount = verificationData.EmployeesCount,
                    GrossSalary = verificationData.GrossSalary,
                    NetSalary = verificationData.NetSalary,
                    ProofType = verificationData.ProofType,
                    VerificationDate = verificationData.VerificationDate,
                    AdditionalNotes = verificationData.AdditionalNotes
                };

                return Ok(resultDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating verification data for lead {LeadId}", leadId);
                return StatusCode(500, new { Message = "An error occurred while creating verification data." });
            }
        }

        [HttpPut("leads/{leadId}")]
        [Authorize(Roles = "Agent")]
        public async Task<ActionResult<VerificationDataDto>> UpdateVerificationData(int leadId, [FromBody] UpdateVerificationDataDto updateDto)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                // Verify lead exists and is assigned to current agent
                var lead = await _context.Leads.FindAsync(leadId);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                if (lead.AssignedTo != currentUserId)
                {
                    return Forbid("You can only update verification data for leads assigned to you");
                }

                // Get existing verification data
                var verificationData = await _context.VerificationData
                    .FirstOrDefaultAsync(vd => vd.LeadId == leadId);

                if (verificationData == null)
                {
                    return NotFound(new { Message = "Verification data not found for this lead" });
                }

                // Update verification data
                verificationData.AgentName = updateDto.AgentName;
                verificationData.AgentContact = updateDto.AgentContact;
                verificationData.AddressConfirmed = updateDto.AddressConfirmed;
                verificationData.PersonMet = updateDto.PersonMet;
                verificationData.Relationship = updateDto.Relationship;
                verificationData.OfficeAddress = updateDto.OfficeAddress;
                verificationData.OfficeState = updateDto.OfficeState;
                verificationData.OfficeDistrict = updateDto.OfficeDistrict;
                verificationData.OfficePincode = updateDto.OfficePincode;
                verificationData.Landmark = updateDto.Landmark;
                verificationData.CompanyType = updateDto.CompanyType;
                verificationData.BusinessNature = updateDto.BusinessNature;
                verificationData.EstablishmentYear = updateDto.EstablishmentYear;
                verificationData.EmployeesCount = updateDto.EmployeesCount;
                verificationData.GrossSalary = updateDto.GrossSalary;
                verificationData.NetSalary = updateDto.NetSalary;
                verificationData.ProofType = updateDto.ProofType;
                verificationData.AdditionalNotes = updateDto.AdditionalNotes;

                await _context.SaveChangesAsync();

                var resultDto = new VerificationDataDto
                {
                    VerificationId = verificationData.VerificationId,
                    LeadId = verificationData.LeadId,
                    AgentName = verificationData.AgentName,
                    AgentContact = verificationData.AgentContact,
                    AddressConfirmed = verificationData.AddressConfirmed,
                    PersonMet = verificationData.PersonMet,
                    Relationship = verificationData.Relationship,
                    OfficeAddress = verificationData.OfficeAddress,
                    OfficeState = verificationData.OfficeState,
                    OfficeDistrict = verificationData.OfficeDistrict,
                    OfficePincode = verificationData.OfficePincode,
                    Landmark = verificationData.Landmark,
                    CompanyType = verificationData.CompanyType,
                    BusinessNature = verificationData.BusinessNature,
                    EstablishmentYear = verificationData.EstablishmentYear,
                    EmployeesCount = verificationData.EmployeesCount,
                    GrossSalary = verificationData.GrossSalary,
                    NetSalary = verificationData.NetSalary,
                    ProofType = verificationData.ProofType,
                    VerificationDate = verificationData.VerificationDate,
                    AdditionalNotes = verificationData.AdditionalNotes
                };

                return Ok(resultDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating verification data for lead {LeadId}", leadId);
                return StatusCode(500, new { Message = "An error occurred while updating verification data." });
            }
        }

        [HttpGet("leads/{leadId}")]
        [Authorize]
        public async Task<ActionResult<VerificationDataDto>> GetVerificationData(int leadId)
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

                // Verify lead access
                var lead = await _context.Leads.FindAsync(leadId);
                if (lead == null)
                {
                    return NotFound(new { Message = "Lead not found" });
                }

                // Role-based authorization
                if (currentUserRole == "Agent" && lead.AssignedTo != currentUserId)
                {
                    return Forbid("You can only access verification data for leads assigned to you");
                }

                var verificationData = await _context.VerificationData
                    .FirstOrDefaultAsync(vd => vd.LeadId == leadId);

                if (verificationData == null)
                {
                    return NotFound(new { Message = "Verification data not found for this lead" });
                }

                var resultDto = new VerificationDataDto
                {
                    VerificationId = verificationData.VerificationId,
                    LeadId = verificationData.LeadId,
                    AgentName = verificationData.AgentName,
                    AgentContact = verificationData.AgentContact,
                    AddressConfirmed = verificationData.AddressConfirmed,
                    PersonMet = verificationData.PersonMet,
                    Relationship = verificationData.Relationship,
                    OfficeAddress = verificationData.OfficeAddress,
                    OfficeState = verificationData.OfficeState,
                    OfficeDistrict = verificationData.OfficeDistrict,
                    OfficePincode = verificationData.OfficePincode,
                    Landmark = verificationData.Landmark,
                    CompanyType = verificationData.CompanyType,
                    BusinessNature = verificationData.BusinessNature,
                    EstablishmentYear = verificationData.EstablishmentYear,
                    EmployeesCount = verificationData.EmployeesCount,
                    GrossSalary = verificationData.GrossSalary,
                    NetSalary = verificationData.NetSalary,
                    ProofType = verificationData.ProofType,
                    VerificationDate = verificationData.VerificationDate,
                    AdditionalNotes = verificationData.AdditionalNotes
                };

                return Ok(resultDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verification data for lead {LeadId}", leadId);
                return StatusCode(500, new { Message = "An error occurred while retrieving verification data." });
            }
        }

        [HttpGet("agent/dashboard-stats")]
        [Authorize(Roles = "Agent")]
        public async Task<ActionResult<AgentDashboardStatsDto>> GetAgentDashboardStats()
        {
            try
            {
                var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                var stats = await _context.Leads
                    .Where(l => l.AssignedTo == currentUserId)
                    .GroupBy(l => 1)
                    .Select(g => new AgentDashboardStatsDto
                    {
                        PendingLeads = g.Count(l => l.Status == "assigned"),
                        InProgressLeads = g.Count(l => l.Status == "in-progress"),
                        CompletedLeads = g.Count(l => l.Status == "approved"),
                        RejectedLeads = g.Count(l => l.Status == "rejected"),
                        TotalAssigned = g.Count()
                    })
                    .FirstOrDefaultAsync();

                return Ok(stats ?? new AgentDashboardStatsDto());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting agent dashboard stats");
                return StatusCode(500, new { Message = "An error occurred while retrieving agent dashboard statistics." });
            }
        }

        [HttpGet("supervisor/dashboard-stats")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult<SupervisorDashboardStatsDto>> GetSupervisorDashboardStats()
        {
            try
            {
                var today = DateTime.Today;

                var stats = await _context.Leads
                    .GroupBy(l => 1)
                    .Select(g => new SupervisorDashboardStatsDto
                    {
                        PendingReviews = g.Count(l => l.Status == "pending-review"),
                        ApprovedToday = g.Count(l => l.Status == "approved" && l.ApprovedDate.HasValue && l.ApprovedDate.Value.Date == today),
                        RejectedToday = g.Count(l => l.Status == "rejected" && l.RejectedDate.HasValue && l.RejectedDate.Value.Date == today),
                        TotalReviewed = g.Count(l => l.Status == "approved" || l.Status == "rejected"),
                        ApprovalRate = g.Count(l => l.Status == "approved" || l.Status == "rejected") > 0 
                            ? (decimal)g.Count(l => l.Status == "approved") / g.Count(l => l.Status == "approved" || l.Status == "rejected") * 100 
                            : 0
                    })
                    .FirstOrDefaultAsync();

                // Get agent performance data
                var agentPerformance = await _context.Users
                    .Where(u => u.Role == "Agent" && u.IsActive)
                    .Select(u => new AgentPerformanceDto
                    {
                        AgentId = u.UserId,
                        AgentName = $"{u.FirstName} {u.LastName}",
                        AssignedCount = u.AssignedLeads.Count(),
                        CompletedCount = u.AssignedLeads.Count(l => l.Status == "approved" || l.Status == "rejected"),
                        ApprovedCount = u.AssignedLeads.Count(l => l.Status == "approved"),
                        RejectedCount = u.AssignedLeads.Count(l => l.Status == "rejected"),
                        CompletionRate = u.AssignedLeads.Count() > 0 
                            ? (decimal)u.AssignedLeads.Count(l => l.Status == "approved" || l.Status == "rejected") / u.AssignedLeads.Count() * 100 
                            : 0,
                        ApprovalRate = u.AssignedLeads.Count(l => l.Status == "approved" || l.Status == "rejected") > 0 
                            ? (decimal)u.AssignedLeads.Count(l => l.Status == "approved") / u.AssignedLeads.Count(l => l.Status == "approved" || l.Status == "rejected") * 100 
                            : 0
                    })
                    .ToListAsync();

                if (stats != null)
                {
                    stats.AgentPerformance = agentPerformance;
                }

                return Ok(stats ?? new SupervisorDashboardStatsDto { AgentPerformance = agentPerformance });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supervisor dashboard stats");
                return StatusCode(500, new { Message = "An error occurred while retrieving supervisor dashboard statistics." });
            }
        }
    }
}
