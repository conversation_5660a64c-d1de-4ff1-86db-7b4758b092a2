import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './contexts/AuthContext';
import { theme } from './theme/theme';
import ProtectedRoute from './components/common/ProtectedRoute';
import Layout from './components/layout/Layout';
import LoginPage from './pages/auth/LoginPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import LeadsPage from './pages/leads/LeadsPage';
import LeadDetailsPage from './pages/leads/LeadDetailsPage';
import CreateLeadForm from './pages/leads/CreateLeadForm';
import DocumentsPage from './pages/documents/DocumentsPage';
import VerificationPage from './pages/verification/VerificationPage';
import ProfilePage from './pages/profile/ProfilePage';
import NotFoundPage from './pages/common/NotFoundPage';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<LoginPage />} />
            
            {/* Protected routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              {/* Dashboard */}
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<DashboardPage />} />
              
              {/* Leads Management */}
              <Route path="leads" element={<LeadsPage />} />
              <Route path="leads/create" element={<CreateLeadForm />} />
              <Route path="leads/:id" element={<LeadDetailsPage />} />
              
              {/* Documents */}
              <Route path="documents" element={<DocumentsPage />} />
              
              {/* Verification */}
              <Route path="verification" element={<VerificationPage />} />
              <Route path="verification/:leadId" element={<VerificationPage />} />
              
              {/* Profile */}
              <Route path="profile" element={<ProfilePage />} />
            </Route>
            
            {/* 404 Page */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
