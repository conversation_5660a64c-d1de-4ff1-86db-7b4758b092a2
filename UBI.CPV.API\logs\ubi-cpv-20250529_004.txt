2025-05-29 18:38:55.728 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:38:55.856 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 18:38:55.877 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:38:56.040 +05:30 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [__EFMigrationsHistory] (
    [MigrationId] nvarchar(150) NOT NULL,
    [ProductVersion] nvarchar(32) NOT NULL,
    CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
);
2025-05-29 18:38:56.052 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:38:56.070 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 18:38:56.121 +05:30 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 18:38:56.153 +05:30 [INF] Applying migration '20250529130711_InitialCreate'.
2025-05-29 18:38:56.291 +05:30 [ERR] Failed executing DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
);
2025-05-29 18:38:56.308 +05:30 [ERR] An error occurred while creating the database or seeding data
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:78b9cce1-bfd3-43c0-9e96-c8d4ed1db502
Error Number:2714,State:6,Class:16
2025-05-29 18:38:56.340 +05:30 [INF] UBI-CPV API starting up...
2025-05-29 18:38:56.375 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 18:38:56.640 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-29 18:38:56.649 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-29 18:38:56.660 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 18:38:56.665 +05:30 [INF] Hosting environment: Development
2025-05-29 18:38:56.671 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-05-29 18:39:21.220 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger - null null
2025-05-29 18:39:21.325 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger - 301 0 null 106.1584ms
2025-05-29 18:39:21.353 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null
2025-05-29 18:39:21.421 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 68.0438ms
2025-05-29 18:39:21.499 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null
2025-05-29 18:39:21.559 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null
2025-05-29 18:39:21.559 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null
2025-05-29 18:39:21.567 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null
2025-05-29 18:39:21.572 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-29 18:39:21.624 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 124.8693ms
2025-05-29 18:39:21.629 +05:30 [INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css'
2025-05-29 18:39:21.631 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-29 18:39:21.639 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-29 18:39:21.674 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 114.9409ms
2025-05-29 18:39:21.676 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 108.9907ms
2025-05-29 18:39:21.678 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 118.7596ms
2025-05-29 18:39:21.914 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null
2025-05-29 18:39:22.094 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 179.9137ms
2025-05-29 18:39:51.748 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null
2025-05-29 18:39:51.765 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 17.3231ms
2025-05-29 18:39:51.945 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null
2025-05-29 18:39:51.952 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null
2025-05-29 18:39:52.009 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-29 18:39:52.031 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 79.2948ms
2025-05-29 18:39:52.109 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 163.3942ms
[2025-05-29 18:45:00.305 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.478 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.510 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.534 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.578 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId]; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.647 +05:30 INF] Applying migration '20250529130711_InitialCreate'. {"EventId":{"Id":20402,"Name":"Microsoft.EntityFrameworkCore.Migrations.MigrationApplying"},"SourceContext":"Microsoft.EntityFrameworkCore.Migrations"}
[2025-05-29 18:45:00.841 +05:30 ERR] Failed executing DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
); {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:01.010 +05:30 ERR] An error occurred while creating the database or seeding data {}
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:80448c5e-53d1-4b2e-90e7-f1a13782a954
Error Number:2714,State:6,Class:16
[2025-05-29 18:45:01.117 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-29 18:45:01.223 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-29 18:45:01.800 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.822 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.922 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.928 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.933 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:03.070 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:03.739 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 681.9606ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:03.818 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:15.803 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/swagger/index.html","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:15.903 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 101.0963ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/swagger/index.html","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:15.981 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:16.108 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLE:********","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCUMB8T0DLE"}
[2025-05-29 18:45:16.128 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLF:********","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCUMB8T0DLF"}
[2025-05-29 18:45:16.138 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/_framework/aspnetcore-browser-refresh.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLG:********","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ConnectionId":"0HNCUMB8T0DLG"}
[2025-05-29 18:45:16.140 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLH:********","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCUMB8T0DLH"}
[2025-05-29 18:45:16.214 +05:30 INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:16.320 +05:30 INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUMB8T0DLE:********","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCUMB8T0DLE"}
[2025-05-29 18:45:16.333 +05:30 INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUMB8T0DLF:********","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCUMB8T0DLF"}
[2025-05-29 18:45:16.362 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 381.4265ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:********","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:16.387 +05:30 INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUMB8T0DLH:********","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCUMB8T0DLH"}
[2025-05-29 18:45:16.388 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/_framework/aspnetcore-browser-refresh.js - 200 13774 application/javascript; charset=utf-8 250.5938ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLG:********","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ConnectionId":"0HNCUMB8T0DLG"}
[2025-05-29 18:45:16.429 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 322.1167ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLE:********","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCUMB8T0DLE"}
[2025-05-29 18:45:16.484 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 356.6468ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLF:********","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCUMB8T0DLF"}
[2025-05-29 18:45:16.541 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 401.2766ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLH:********","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCUMB8T0DLH"}
[2025-05-29 18:45:16.569 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/_vs/browserLink - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLF:********","RequestPath":"/_vs/browserLink","ConnectionId":"0HNCUMB8T0DLF"}
[2025-05-29 18:45:16.685 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/_vs/browserLink - 200 null text/javascript; charset=UTF-8 116.0283ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLF:********","RequestPath":"/_vs/browserLink","ConnectionId":"0HNCUMB8T0DLF"}
[2025-05-29 18:45:16.777 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLF:********","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUMB8T0DLF"}
[2025-05-29 18:45:16.799 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLH:********","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCUMB8T0DLH"}
[2025-05-29 18:45:16.844 +05:30 INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCUMB8T0DLH:********","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCUMB8T0DLH"}
[2025-05-29 18:45:16.872 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 73.0612ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLH:********","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCUMB8T0DLH"}
[2025-05-29 18:45:16.896 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 118.7903ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLF:********","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUMB8T0DLF"}
[2025-05-29 18:46:33.431 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 71 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:33.480 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:33.505 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:33.537 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:33.622 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:40.175 +05:30 INF] Executed DbCommand (120ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:53.479 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:55.213 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 21546.1282ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:55.248 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:46:55.293 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 200 null application/json; charset=utf-8 21862.6856ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:16.038 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 71 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:16.107 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:16.126 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:16.149 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:16.169 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:31.446 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:31.630 +05:30 ERR] Error during login for user: admin {"SourceContext":"UBI.CPV.API.Controllers.AuthController","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
BCrypt.Net.SaltParseException: Invalid salt version
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at BCrypt.Net.BCrypt.Verify(String text, String hash, Boolean enhancedEntropy, HashType hashType)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 70
[2025-05-29 18:47:31.659 +05:30 INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:31.680 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 15484.6529ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:31.695 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:31.709 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 500 null application/json; charset=utf-8 15674.3985ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:43.216 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 70 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:43.271 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:43.291 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:43.327 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:43.346 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:47:45.280 +05:30 INF] Executed DbCommand (10ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:03.707 +05:30 ERR] Error during login for user: admin {"SourceContext":"UBI.CPV.API.Controllers.AuthController","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
BCrypt.Net.SaltParseException: Invalid salt version
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at BCrypt.Net.BCrypt.Verify(String text, String hash, Boolean enhancedEntropy, HashType hashType)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 70
[2025-05-29 18:48:03.733 +05:30 INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:03.754 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 20383.6123ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:03.773 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:03.789 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 500 null application/json; charset=utf-8 20572.651ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:05.982 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 70 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:06.013 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:06.027 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:06.044 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:06.058 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:07.819 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:10.718 +05:30 ERR] Error during login for user: admin {"SourceContext":"UBI.CPV.API.Controllers.AuthController","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
BCrypt.Net.SaltParseException: Invalid salt version
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at BCrypt.Net.BCrypt.Verify(String text, String hash, Boolean enhancedEntropy, HashType hashType)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 70
[2025-05-29 18:48:10.768 +05:30 INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:10.811 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 4726.3738ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:10.832 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:10.847 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 500 null application/json; charset=utf-8 4865.1744ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:13.857 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 70 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:13.881 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:13.904 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:13.919 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:13.934 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:48:16.145 +05:30 INF] Executed DbCommand (2ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:50:31.337 +05:30 ERR] Error during login for user: admin {"SourceContext":"UBI.CPV.API.Controllers.AuthController","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
BCrypt.Net.SaltParseException: Invalid salt version
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at BCrypt.Net.BCrypt.Verify(String text, String hash, Boolean enhancedEntropy, HashType hashType)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 70
[2025-05-29 18:50:31.438 +05:30 INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:50:31.456 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 137506.0598ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:50:31.472 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 18:50:31.487 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 500 null application/json; charset=utf-8 137629.2283ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLJ:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLJ"}
[2025-05-29 19:06:15.672 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 70 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:06:15.698 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:06:15.723 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:06:15.743 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:06:15.762 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:06:21.401 +05:30 INF] Executed DbCommand (17ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:19:22.190 +05:30 ERR] Error during login for user: admin {"SourceContext":"UBI.CPV.API.Controllers.AuthController","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
BCrypt.Net.SaltParseException: Invalid salt version
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at BCrypt.Net.BCrypt.Verify(String text, String hash, Boolean enhancedEntropy, HashType hashType)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 70
[2025-05-29 19:19:22.214 +05:30 INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:19:22.228 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 786437.8532ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:19:22.240 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:19:22.253 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 500 null application/json; charset=utf-8 786579.4084ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLL:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLL"}
[2025-05-29 19:22:03.004 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 70 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:03.032 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:03.041 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:03.050 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:03.061 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:06.202 +05:30 INF] Executed DbCommand (7ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:17.780 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:17.799 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 14721.1254ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:17.815 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:17.832 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 200 null application/json; charset=utf-8 14828.3024ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:40.663 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 68 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:40.684 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:40.697 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:40.709 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:40.719 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:43.432 +05:30 INF] Executed DbCommand (11ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:43.849 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:43.919 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:43.947 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 3211.141ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:43.969 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:22:43.990 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 200 null application/json; charset=utf-8 3327.928ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLN:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCUMB8T0DLN"}
[2025-05-29 19:31:30.037 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:30.066 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:30.083 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 46.342ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:30.109 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 55 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:30.131 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:30.144 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:30.160 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.199 +05:30 INF] Executed DbCommand (9ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.480 +05:30 INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.508 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"41a40ffa-1c53-4603-ac43-5903b51af4f5","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.534 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 3346.4329ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.555 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.571 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 200 null application/json; charset=utf-8 3462.7669ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.817 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.894 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:33.911 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM0:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM0"}
[2025-05-29 19:31:33.911 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:33.974 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:33.983 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:33.988 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM0:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM0"}
[2025-05-29 19:31:33.994 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.052 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 236.0877ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.054 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 159.9842ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.055 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 144.2265ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM0:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM0"}
[2025-05-29 19:31:34.057 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 145.9022ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.133 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.140 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.200 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.212 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.364 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.364 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.408 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.409 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.556 +05:30 WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results. {"EventId":{"Id":10103,"Name":"Microsoft.EntityFrameworkCore.Query.FirstWithoutOrderByAndFilterWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.572 +05:30 INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.629 +05:30 INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.657 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.685 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 233.6788ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.699 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.712 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 578.9279ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.723 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.758 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.767 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.801 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.807 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.837 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.848 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 392.2433ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.881 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.893 +05:30 INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.920 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 780.6998ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLU:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLU"}
[2025-05-29 19:31:34.925 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.928 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:34.986 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 108.8051ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:34.991 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.019 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:35.021 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.048 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 325.0903ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLV:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DLV"}
[2025-05-29 19:31:35.053 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.119 +05:30 INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.176 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.204 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.224 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 121.1573ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.245 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:31:35.261 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 333.5803ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DLQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DLQ"}
[2025-05-29 19:32:17.381 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.442 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.476 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 95.4058ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.524 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/leads - application/json 220 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.553 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.606 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.643 +05:30 INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.912 +05:30 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:17.973 +05:30 INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime2), @p5='?' (Size = 100), @p6='?' (Size = 50), @p7='?' (Size = 15), @p8='?' (DbType = DateTime2), @p9='?' (Size = 500), @p10='?' (Size = 1000), @p11='?' (DbType = Int32), @p12='?' (DbType = DateTime2), @p13='?' (DbType = DateTime2), @p14='?' (Size = 20), @p15='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Leads] ([ApprovedDate], [AssignedDate], [AssignedTo], [CreatedBy], [CreatedDate], [CustomerName], [LoanType], [MobileNumber], [RejectedDate], [RejectionReason], [ReviewComments], [ReviewedBy], [ReviewedDate], [StartedDate], [Status], [SubmittedDate])
OUTPUT INSERTED.[LeadId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:18.137 +05:30 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:18.226 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@p0='?' (Size = 500), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (Size = 50), @p4='?' (Size = 200), @p5='?' (DbType = Int32), @p6='?' (Size = 10), @p7='?' (Size = 50), @p8='?' (Size = 500), @p9='?' (DbType = Int32), @p10='?' (Size = 20), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [LeadAddresses] ([Address], [AddressType], [CreatedDate], [District], [Landmark], [LeadId], [Pincode], [State])
OUTPUT INSERTED.[AddressId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
INSERT INTO [LeadStatusHistory] ([Comments], [LeadId], [Status], [Timestamp], [UpdatedBy])
OUTPUT INSERTED.[HistoryId]
VALUES (@p8, @p9, @p10, @p11, @p12); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:18.315 +05:30 ERR] An exception occurred in the database while saving changes for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__LeadAddre__Addre__5EBF139D". The conflict occurred in database "UBI_CPV_DB", table "dbo.LeadAddresses", column 'AddressType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:981d45ea-1082-42a1-9574-8d67416afa19
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) {"EventId":{"Id":10000,"Name":"Microsoft.EntityFrameworkCore.Update.SaveChangesFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Update","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__LeadAddre__Addre__5EBF139D". The conflict occurred in database "UBI_CPV_DB", table "dbo.LeadAddresses", column 'AddressType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:981d45ea-1082-42a1-9574-8d67416afa19
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-05-29 19:32:18.868 +05:30 ERR] Error creating lead {"SourceContext":"UBI.CPV.API.Controllers.LeadsController","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__LeadAddre__Addre__5EBF139D". The conflict occurred in database "UBI_CPV_DB", table "dbo.LeadAddresses", column 'AddressType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:981d45ea-1082-42a1-9574-8d67416afa19
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.LeadsController.CreateLead(CreateLeadDto createLeadDto) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\LeadsController.cs:line 347
[2025-05-29 19:32:19.010 +05:30 INF] Executing ObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:19.078 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 1396.3629ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:19.105 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:32:19.123 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/leads - 500 null application/json; charset=utf-8 1598.9781ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM2:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM2"}
[2025-05-29 19:34:15.826 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM6:********","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUMB8T0DM6"}
[2025-05-29 19:34:16.041 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 215.8346ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM6:********","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCUMB8T0DM6"}
[2025-05-29 19:34:16.210 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/favicon.ico - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM6:********","RequestPath":"/favicon.ico","ConnectionId":"0HNCUMB8T0DM6"}
[2025-05-29 19:34:16.256 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/favicon.ico - 404 0 null 46.8124ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM6:********","RequestPath":"/favicon.ico","ConnectionId":"0HNCUMB8T0DM6"}
[2025-05-29 19:34:16.307 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/favicon.ico, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM6:********","RequestPath":"/favicon.ico","ConnectionId":"0HNCUMB8T0DM6"}
[2025-05-29 19:35:30.854 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:30.900 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:30.940 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 85.6652ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:30.995 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/leads - application/json 220 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:31.019 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:31.043 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:31.064 +05:30 INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:48.133 +05:30 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:35:48.199 +05:30 INF] Executed DbCommand (47ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime2), @p5='?' (Size = 100), @p6='?' (Size = 50), @p7='?' (Size = 15), @p8='?' (DbType = DateTime2), @p9='?' (Size = 500), @p10='?' (Size = 1000), @p11='?' (DbType = Int32), @p12='?' (DbType = DateTime2), @p13='?' (DbType = DateTime2), @p14='?' (Size = 20), @p15='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Leads] ([ApprovedDate], [AssignedDate], [AssignedTo], [CreatedBy], [CreatedDate], [CustomerName], [LoanType], [MobileNumber], [RejectedDate], [RejectionReason], [ReviewComments], [ReviewedBy], [ReviewedDate], [StartedDate], [Status], [SubmittedDate])
OUTPUT INSERTED.[LeadId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:36:03.938 +05:30 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:36:03.995 +05:30 INF] Executed DbCommand (41ms) [Parameters=[@p0='?' (Size = 500), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (Size = 50), @p4='?' (Size = 200), @p5='?' (DbType = Int32), @p6='?' (Size = 10), @p7='?' (Size = 50), @p8='?' (Size = 500), @p9='?' (DbType = Int32), @p10='?' (Size = 20), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [LeadAddresses] ([Address], [AddressType], [CreatedDate], [District], [Landmark], [LeadId], [Pincode], [State])
OUTPUT INSERTED.[AddressId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
INSERT INTO [LeadStatusHistory] ([Comments], [LeadId], [Status], [Timestamp], [UpdatedBy])
OUTPUT INSERTED.[HistoryId]
VALUES (@p8, @p9, @p10, @p11, @p12); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:36:04.031 +05:30 ERR] An exception occurred in the database while saving changes for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__LeadAddre__Addre__5EBF139D". The conflict occurred in database "UBI_CPV_DB", table "dbo.LeadAddresses", column 'AddressType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:981d45ea-1082-42a1-9574-8d67416afa19
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) {"EventId":{"Id":10000,"Name":"Microsoft.EntityFrameworkCore.Update.SaveChangesFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Update","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__LeadAddre__Addre__5EBF139D". The conflict occurred in database "UBI_CPV_DB", table "dbo.LeadAddresses", column 'AddressType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:981d45ea-1082-42a1-9574-8d67416afa19
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-05-29 19:38:38.719 +05:30 ERR] Error creating lead {"SourceContext":"UBI.CPV.API.Controllers.LeadsController","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint "CK__LeadAddre__Addre__5EBF139D". The conflict occurred in database "UBI_CPV_DB", table "dbo.LeadAddresses", column 'AddressType'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:981d45ea-1082-42a1-9574-8d67416afa19
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.LeadsController.CreateLead(CreateLeadDto createLeadDto) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\LeadsController.cs:line 347
[2025-05-29 19:38:38.815 +05:30 INF] Executing ObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:38.846 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 187738.1732ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:38.865 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:38.882 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/leads - 500 null application/json; charset=utf-8 187886.4403ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:55.913 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:55.987 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:56.031 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 117.6082ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:56.086 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/leads - application/json 215 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:56.131 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:56.176 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:56.229 +05:30 INF] Route matched with {action = "CreateLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] CreateLead(UBI.CPV.API.Models.DTOs.CreateLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:58.927 +05:30 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:59.010 +05:30 INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime2), @p5='?' (Size = 100), @p6='?' (Size = 50), @p7='?' (Size = 15), @p8='?' (DbType = DateTime2), @p9='?' (Size = 500), @p10='?' (Size = 1000), @p11='?' (DbType = Int32), @p12='?' (DbType = DateTime2), @p13='?' (DbType = DateTime2), @p14='?' (Size = 20), @p15='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Leads] ([ApprovedDate], [AssignedDate], [AssignedTo], [CreatedBy], [CreatedDate], [CustomerName], [LoanType], [MobileNumber], [RejectedDate], [RejectionReason], [ReviewComments], [ReviewedBy], [ReviewedDate], [StartedDate], [Status], [SubmittedDate])
OUTPUT INSERTED.[LeadId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:59.058 +05:30 WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'. {"EventId":{"Id":30004,"Name":"Microsoft.EntityFrameworkCore.Database.Transaction.SavepointsDisabledBecauseOfMARS"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Transaction","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:59.097 +05:30 INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (Size = 500), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (Size = 50), @p4='?' (Size = 200), @p5='?' (DbType = Int32), @p6='?' (Size = 10), @p7='?' (Size = 50), @p8='?' (Size = 500), @p9='?' (DbType = Int32), @p10='?' (Size = 20), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [LeadAddresses] ([Address], [AddressType], [CreatedDate], [District], [Landmark], [LeadId], [Pincode], [State])
OUTPUT INSERTED.[AddressId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
INSERT INTO [LeadStatusHistory] ([Comments], [LeadId], [Status], [Timestamp], [UpdatedBy])
OUTPUT INSERTED.[HistoryId]
VALUES (@p8, @p9, @p10, @p11, @p12); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:59.324 +05:30 WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'. {"EventId":{"Id":20504,"Name":"Microsoft.EntityFrameworkCore.Query.MultipleCollectionIncludeWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:38:59.540 +05:30 INF] Executed DbCommand (131ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:02.919 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"86f5b4b8-8b11-4233-975d-df168c5be0e5","ActionName":"UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:02.961 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API) in 6660.0142ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:02.986 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.CreateLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:03.001 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/leads - 200 null application/json; charset=utf-8 6914.2079ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:05.562 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:05.691 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:05.711 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:39:05.718 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:05.736 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:05.780 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:05.797 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:39:05.803 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:05.819 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 256.6337ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:05.851 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 132.9875ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:05.873 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 162.0904ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:39:05.877 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 186.2675ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:05.917 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:05.974 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.024 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.059 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.062 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.088 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.091 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.138 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.138 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.240 +05:30 INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.251 +05:30 INF] Executed DbCommand (41ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.299 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.304 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.352 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 162.2938ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.360 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 232.7886ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.388 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.392 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.425 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 507.9733ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.428 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 453.4583ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:39:06.438 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.492 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.505 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.537 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.544 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.571 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.574 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.618 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.618 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.648 +05:30 INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.704 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.723 +05:30 INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.790 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 177.4421ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.798 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.835 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 121.3624ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.840 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.875 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 436.5889ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:06.878 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:06.935 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 430.3859ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.229 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.229 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:11.308 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.326 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:11.384 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 155.3394ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.388 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 158.3755ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:39:11.464 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.504 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.520 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.546 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:11.598 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.839 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.884 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 16296.7759ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.902 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.914 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 16449.989ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.936 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.952 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.963 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:27.973 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:28.050 +05:30 INF] Executed DbCommand (58ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:28.099 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:28.118 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 127.8534ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:28.137 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:39:28.157 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 220.9488ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000A","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:07.946 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:07.947 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:07.968 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:07.992 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:08.101 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.110 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.118 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:08.129 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:08.198 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 251.9475ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.217 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 270.427ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.325 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 357.4599ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:08.328 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 336.1938ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:08.336 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.344 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.459 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.465 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.493 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.497 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.548 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.609 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.649 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.826 +05:30 INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.848 +05:30 INF] Executed DbCommand (21ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:08.959 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:08.982 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:09.072 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 257.0368ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.077 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 454.6698ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:09.121 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.126 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:09.162 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 825.3015ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.164 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 820.0015ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:09.176 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.293 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.330 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.418 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.425 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.505 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.512 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.612 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.634 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.747 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.758 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.807 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.814 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.862 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 121.7715ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.868 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 245.0655ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.909 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.914 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:09.966 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 672.8284ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:09.968 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 791.8612ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:29.645 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:29.647 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:29.647 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:29.647 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:29.748 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:29.743 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:29.779 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:29.875 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:29.942 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 295.5383ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.026 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 378.9574ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.097 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 450.8033ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:30.099 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 453.729ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:30.109 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.116 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.224 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.232 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.295 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.298 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.341 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.344 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.398 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.413 +05:30 INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.480 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.490 +05:30 INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.538 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 146.9427ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.545 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.594 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.598 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 194.815ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.637 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 520.6938ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.640 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.648 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:30.713 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 603.6201ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:30.720 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:30.724 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.788 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:30.797 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.832 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:30.834 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.909 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:30.911 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:30.979 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:31.008 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:31.254 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:31.260 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 357.9877ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:31.333 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:31.338 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:31.387 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 419.7267ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:31.390 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 741.555ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:31.430 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:31.459 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 734.5867ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:38.778 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:38.778 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:38.778 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:38.779 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:38.911 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:38.920 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:38.928 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DM8:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:38.938 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:39.046 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 268.2955ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.049 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 270.7099ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.052 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 273.433ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DM8:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DM8"}
[2025-05-29 19:40:39.056 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 277.472ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:39.211 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.196 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.306 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.322 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.385 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.388 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.429 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.432 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.498 +05:30 INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.511 +05:30 INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.590 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.629 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.629 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 142.5429ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.688 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.691 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.746 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 245.9551ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.749 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 537.3563ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.760 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:39.812 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.830 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:39.867 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 670.7648ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DME:********","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DME"}
[2025-05-29 19:40:39.871 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:39.877 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.945 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:39.951 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.992 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:39.999 +05:30 INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:40.044 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:40.051 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3ea4da70-5a5a-43d2-8c6f-a7a9b542aabf","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:40.121 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:40.123 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 135.4802ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:40.190 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:40.215 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 455.1348ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:40.218 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:40.309 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"3c3e1acc-acce-4211-a1e3-fb47a0b66d39","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:40.353 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 242.064ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:40.396 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:40.434 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 556.6053ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:42.592 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:0000000B","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:42.594 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:42.695 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:0000000B","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:42.711 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:42.790 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 197.875ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:0000000B","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:42.795 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/3 - 204 null null 200.592ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:42.880 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:42.925 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:42.948 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:42.976 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:43.084 +05:30 INF] Executed DbCommand (69ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:43.145 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:43.201 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 189.3741ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:43.247 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:43.282 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 401.5934ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMC:0000000C","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMC"}
[2025-05-29 19:40:43.300 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/3 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.385 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.421 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.451 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.494 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.544 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d68fba35-6ab5-48c5-a870-67436b228593","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.579 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 95.1737ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.614 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
[2025-05-29 19:40:43.645 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/3 - 200 null application/json; charset=utf-8 345.1922ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DMD:********","RequestPath":"/api/leads/3","ConnectionId":"0HNCUMB8T0DMD"}
