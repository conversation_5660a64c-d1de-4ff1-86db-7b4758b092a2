{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\UBI-CPV-T\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { theme } from './theme/theme';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport Layout from './components/layout/Layout';\nimport LoginPage from './pages/auth/LoginPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport LeadsPage from './pages/leads/LeadsPage';\nimport LeadDetailsPage from './pages/leads/LeadDetailsPage';\nimport DocumentsPage from './pages/documents/DocumentsPage';\nimport VerificationPage from './pages/verification/VerificationPage';\nimport ProfilePage from './pages/profile/ProfilePage';\nimport NotFoundPage from './pages/common/NotFoundPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 17\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              index: true,\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"dashboard\",\n              element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"leads\",\n              element: /*#__PURE__*/_jsxDEV(LeadsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"leads/create\",\n              element: /*#__PURE__*/_jsxDEV(CreateLeadPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"leads/:id\",\n              element: /*#__PURE__*/_jsxDEV(LeadDetailsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"documents\",\n              element: /*#__PURE__*/_jsxDEV(DocumentsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"verification\",\n              element: /*#__PURE__*/_jsxDEV(VerificationPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"verification/:leadId\",\n              element: /*#__PURE__*/_jsxDEV(VerificationPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 59\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"profile\",\n              element: /*#__PURE__*/_jsxDEV(ProfilePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(NotFoundPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ThemeProvider", "CssBaseline", "<PERSON>th<PERSON><PERSON><PERSON>", "theme", "ProtectedRoute", "Layout", "LoginPage", "DashboardPage", "LeadsPage", "LeadDetailsPage", "DocumentsPage", "VerificationPage", "ProfilePage", "NotFoundPage", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "index", "to", "replace", "CreateLeadPage", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/UBI-CPV-T/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { theme } from './theme/theme';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport Layout from './components/layout/Layout';\nimport LoginPage from './pages/auth/LoginPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport LeadsPage from './pages/leads/LeadsPage';\nimport LeadDetailsPage from './pages/leads/LeadDetailsPage';\nimport CreateLeadForm from './pages/leads/CreateLeadForm';\nimport DocumentsPage from './pages/documents/DocumentsPage';\nimport VerificationPage from './pages/verification/VerificationPage';\nimport ProfilePage from './pages/profile/ProfilePage';\nimport NotFoundPage from './pages/common/NotFoundPage';\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <AuthProvider>\n        <Router>\n          <Routes>\n            {/* Public routes */}\n            <Route path=\"/login\" element={<LoginPage />} />\n            \n            {/* Protected routes */}\n            <Route\n              path=\"/\"\n              element={\n                <ProtectedRoute>\n                  <Layout />\n                </ProtectedRoute>\n              }\n            >\n              {/* Dashboard */}\n              <Route index element={<Navigate to=\"/dashboard\" replace />} />\n              <Route path=\"dashboard\" element={<DashboardPage />} />\n              \n              {/* Leads Management */}\n              <Route path=\"leads\" element={<LeadsPage />} />\n              <Route path=\"leads/create\" element={<CreateLeadPage />} />\n              <Route path=\"leads/:id\" element={<LeadDetailsPage />} />\n              \n              {/* Documents */}\n              <Route path=\"documents\" element={<DocumentsPage />} />\n              \n              {/* Verification */}\n              <Route path=\"verification\" element={<VerificationPage />} />\n              <Route path=\"verification/:leadId\" element={<VerificationPage />} />\n              \n              {/* Profile */}\n              <Route path=\"profile\" element={<ProfilePage />} />\n            </Route>\n            \n            {/* 404 Page */}\n            <Route path=\"*\" element={<NotFoundPage />} />\n          </Routes>\n        </Router>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,eAAe,MAAM,+BAA+B;AAE3D,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,gBAAgB,MAAM,uCAAuC;AACpE,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,YAAY,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACf,aAAa;IAACG,KAAK,EAAEA,KAAM;IAAAc,QAAA,gBAC1BF,OAAA,CAACd,WAAW;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfN,OAAA,CAACb,YAAY;MAAAe,QAAA,eACXF,OAAA,CAACnB,MAAM;QAAAqB,QAAA,eACLF,OAAA,CAAClB,MAAM;UAAAoB,QAAA,gBAELF,OAAA,CAACjB,KAAK;YAACwB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAER,OAAA,CAACT,SAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG/CN,OAAA,CAACjB,KAAK;YACJwB,IAAI,EAAC,GAAG;YACRC,OAAO,eACLR,OAAA,CAACX,cAAc;cAAAa,QAAA,eACbF,OAAA,CAACV,MAAM;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACjB;YAAAJ,QAAA,gBAGDF,OAAA,CAACjB,KAAK;cAAC0B,KAAK;cAACD,OAAO,eAAER,OAAA,CAAChB,QAAQ;gBAAC0B,EAAE,EAAC,YAAY;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAER,OAAA,CAACR,aAAa;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGtDN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,OAAO;cAACC,OAAO,eAAER,OAAA,CAACP,SAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,cAAc;cAACC,OAAO,eAAER,OAAA,CAACY,cAAc;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAER,OAAA,CAACN,eAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGxDN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAER,OAAA,CAACL,aAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGtDN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,cAAc;cAACC,OAAO,eAAER,OAAA,CAACJ,gBAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,sBAAsB;cAACC,OAAO,eAAER,OAAA,CAACJ,gBAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGpEN,OAAA,CAACjB,KAAK;cAACwB,IAAI,EAAC,SAAS;cAACC,OAAO,eAAER,OAAA,CAACH,WAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAGRN,OAAA,CAACjB,KAAK;YAACwB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAER,OAAA,CAACF,YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACO,EAAA,GA9CQZ,GAAG;AAgDZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}