{"ast": null, "code": "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    var next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n      next = next.parentNode || next.host;\n    } while (next);\n  } // Give up, the result is false\n\n  return false;\n}", "map": {"version": 3, "names": ["isShadowRoot", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "parentNode", "host"], "sources": ["D:/Augment-projects/UBI-CPV-T/node_modules/@popperjs/core/lib/dom-utils/contains.js"], "sourcesContent": ["import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC9C,IAAIC,QAAQ,GAAGD,KAAK,CAACE,WAAW,IAAIF,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEzD,IAAIH,MAAM,CAACD,QAAQ,CAACE,KAAK,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb,CAAC,CAAC;EAAA,KACG,IAAIC,QAAQ,IAAIJ,YAAY,CAACI,QAAQ,CAAC,EAAE;IACzC,IAAIE,IAAI,GAAGH,KAAK;IAEhB,GAAG;MACD,IAAIG,IAAI,IAAIJ,MAAM,CAACK,UAAU,CAACD,IAAI,CAAC,EAAE;QACnC,OAAO,IAAI;MACb,CAAC,CAAC;;MAGFA,IAAI,GAAGA,IAAI,CAACE,UAAU,IAAIF,IAAI,CAACG,IAAI;IACrC,CAAC,QAAQH,IAAI;EACf,CAAC,CAAC;;EAGJ,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}