using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace UBI.CPV.API.Models.Entities
{
    public class VerificationData
    {
        [Key]
        public int VerificationId { get; set; }

        [Required]
        public int LeadId { get; set; }

        [Required]
        [StringLength(100)]
        public string AgentName { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        public string AgentContact { get; set; } = string.Empty;

        [StringLength(10)]
        public string? AddressConfirmed { get; set; }

        [StringLength(100)]
        public string? PersonMet { get; set; }

        [StringLength(50)]
        public string? Relationship { get; set; }

        [StringLength(500)]
        public string? OfficeAddress { get; set; }

        [StringLength(50)]
        public string? OfficeState { get; set; }

        [StringLength(50)]
        public string? OfficeDistrict { get; set; }

        [StringLength(10)]
        public string? OfficePincode { get; set; }

        [StringLength(200)]
        public string? Landmark { get; set; }

        [StringLength(100)]
        public string? CompanyType { get; set; }

        [StringLength(100)]
        public string? BusinessNature { get; set; }

        public int? EstablishmentYear { get; set; }

        [StringLength(20)]
        public string? EmployeesCount { get; set; }

        [Column(TypeName = "decimal(15,2)")]
        public decimal? GrossSalary { get; set; }

        [Column(TypeName = "decimal(15,2)")]
        public decimal? NetSalary { get; set; }

        [StringLength(50)]
        public string? ProofType { get; set; }

        public DateTime VerificationDate { get; set; } = DateTime.UtcNow;

        [StringLength(1000)]
        public string? AdditionalNotes { get; set; }

        // Navigation properties
        [ForeignKey("LeadId")]
        public virtual Lead Lead { get; set; } = null!;
    }

    public class AppSetting
    {
        [Key]
        public int SettingId { get; set; }

        [Required]
        [StringLength(100)]
        public string SettingKey { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string SettingValue { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Description { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public int? UpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey("UpdatedBy")]
        public virtual User? UpdatedByUser { get; set; }
    }

    public class AuditLog
    {
        [Key]
        public int LogId { get; set; }

        public int? UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty;

        [StringLength(50)]
        public string? TableName { get; set; }

        public int? RecordId { get; set; }

        public string? OldValues { get; set; }

        public string? NewValues { get; set; }

        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [StringLength(45)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }
    }
}
